/**
 * Git分支面板组件
 * 显示和管理Git分支
 */
import React, { useState } from 'react';
import { List, Button, Tooltip, Space, Tag, Modal, Input, message, Tabs, Select } from 'antd';
import type { TabsProps } from 'antd';
import {
  BranchesOutlined,
  PlusOutlined,
  DeleteOutlined,
  CheckOutlined,
  MergeCellsOutlined,
  CloudUploadOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { GitBranch } from '../../services/GitService';
import './GitBranchPanel.less';

const { Option } = Select;

/**
 * Git分支面板组件
 */
const GitBranchPanel: React.FC = () => {
  const { t } = useTranslation();

  // 安全地获取Git状态
  const gitState = useSelector((state: RootState) => {
    try {
      return state.git;
    } catch (error) {
      console.warn('Git state access error:', error);
      return { branches: [], currentBranch: '' };
    }
  });

  const { branches, currentBranch } = gitState;

  const [activeTab, setActiveTab] = useState<string>('local');
  const [isCreateBranchModalVisible, setIsCreateBranchModalVisible] = useState<boolean>(false);
  const [isMergeBranchModalVisible, setIsMergeBranchModalVisible] = useState<boolean>(false);
  const [newBranchName, setNewBranchName] = useState<string>('');
  const [selectedBranchForMerge, setSelectedBranchForMerge] = useState<string>('');

  // 获取本地分支 - 确保branches是数组且每个branch对象有效
  const safeBranches = Array.isArray(branches) ? branches : [];
  const localBranches = safeBranches.filter(branch => branch && !branch.remote);

  // 获取远程分支
  const remoteBranches = safeBranches.filter(branch => branch && branch.remote);

  // 处理创建分支
  const handleCreateBranch = () => {
    setIsCreateBranchModalVisible(true);
  };

  // 处理确认创建分支
  const handleConfirmCreateBranch = () => {
    if (!newBranchName.trim()) {
      message.error(t('git.branchNameRequired') || '');
      return;
    }

    // 检查分支名是否已存在
    if (branches.some(branch => branch.name === newBranchName)) {
      message.error(t('git.branchAlreadyExists') || '');
      return;
    }

    // 这里应该调用gitService的createBranch方法
    message.success(t('git.branchCreated', { name: newBranchName }) || '');
    setIsCreateBranchModalVisible(false);
    setNewBranchName('');
  };

  // 处理取消创建分支
  const handleCancelCreateBranch = () => {
    setIsCreateBranchModalVisible(false);
    setNewBranchName('');
  };

  // 处理切换分支
  const handleCheckoutBranch = (branchName: string) => {
    if (branchName === currentBranch) {
      message.info(t('git.alreadyOnBranch', { name: branchName }) || '');
      return;
    }

    Modal.confirm({
      title: t('git.checkoutConfirmTitle') || '',
      content: t('git.checkoutConfirmContent', { name: branchName }) || '',
      okText: t('git.checkout') || '',
      cancelText: t('common.cancel') || '',
      onOk: () => {
        // 这里应该调用gitService的checkoutBranch方法
        message.success(t('git.branchCheckedOut', { name: branchName }) || '');
      }});
  };

  // 处理删除分支
  const handleDeleteBranch = (branch: GitBranch) => {
    if (branch.name === currentBranch) {
      message.error(t('git.cannotDeleteCurrentBranch') || '');
      return;
    }

    Modal.confirm({
      title: t('git.deleteConfirmTitle') || '',
      content: t('git.deleteConfirmContent', { name: branch.name }) || '',
      okText: t('git.delete') || '',
      cancelText: t('common.cancel') || '',
      okType: 'danger',
      onOk: () => {
        // 这里应该调用gitService的deleteBranch方法
        message.success(t('git.branchDeleted', { name: branch.name }) || '');
      }});
  };

  // 处理合并分支
  const handleMergeBranch = () => {
    setIsMergeBranchModalVisible(true);
  };

  // 处理确认合并分支
  const handleConfirmMergeBranch = () => {
    if (!selectedBranchForMerge) {
      message.error(t('git.selectBranchToMerge') || '');
      return;
    }

    // 这里应该调用gitService的mergeBranch方法
    message.success(t('git.branchMerged', { name: selectedBranchForMerge }) || '');
    setIsMergeBranchModalVisible(false);
    setSelectedBranchForMerge('');
  };

  // 处理取消合并分支
  const handleCancelMergeBranch = () => {
    setIsMergeBranchModalVisible(false);
    setSelectedBranchForMerge('');
  };

  // 处理推送分支
  const handlePushBranch = (branch: GitBranch) => {
    Modal.confirm({
      title: t('git.pushConfirmTitle') || '',
      content: t('git.pushConfirmContent', { name: branch.name }) || '',
      okText: t('git.push') || '',
      cancelText: t('common.cancel') || '',
      onOk: () => {
        // 这里应该调用gitService的pushBranch方法
        message.success(t('git.branchPushed', { name: branch.name }) || '');
      }});
  };

  // 处理拉取分支
  const handlePullBranch = (branch: GitBranch) => {
    Modal.confirm({
      title: t('git.pullConfirmTitle') || '',
      content: t('git.pullConfirmContent', { name: branch.name }) || '',
      okText: t('git.pull') || '',
      cancelText: t('common.cancel') || '',
      onOk: () => {
        // 这里应该调用gitService的pullBranch方法
        message.success(t('git.branchPulled', { name: branch.name }) || '');
      }});
  };

  // 渲染本地分支列表
  const renderLocalBranches = () => {
    if (localBranches.length === 0) {
      return (
        <div className="git-empty-list">
          <p>{t('git.noLocalBranches') || ''}</p>
        </div>
      );
    }

    return (
      <List
        size="small"
        dataSource={localBranches}
        renderItem={(branch) => (
          <List.Item
            key={branch.name}
            actions={[
              <Tooltip title={t('git.checkout') || ''} key="checkout">
                <Button
                  icon={<CheckOutlined />}
                  size="small"
                  onClick={() => handleCheckoutBranch(branch.name)}
                  disabled={branch.name === currentBranch}
                />
              </Tooltip>,
              <Tooltip title={t('git.push') || ''} key="push">
                <Button
                  icon={<CloudUploadOutlined />}
                  size="small"
                  onClick={() => handlePushBranch(branch)}
                />
              </Tooltip>,
              <Tooltip title={t('git.delete') || ''} key="delete">
                <Button
                  icon={<DeleteOutlined />}
                  size="small"
                  danger
                  onClick={() => handleDeleteBranch(branch)}
                  disabled={branch.name === currentBranch}
                />
              </Tooltip>,
            ]}
          >
            <div className="git-branch-item">
              <BranchesOutlined />
              <span className="git-branch-name">{branch.name}</span>
              {branch.name === currentBranch && (
                <Tag color="green">{t('git.current') || ''}</Tag>
              )}
            </div>
          </List.Item>
        )}
      />
    );
  };

  // 渲染远程分支列表
  const renderRemoteBranches = () => {
    if (remoteBranches.length === 0) {
      return (
        <div className="git-empty-list">
          <p>{t('git.noRemoteBranches') || ''}</p>
        </div>
      );
    }

    return (
      <List
        size="small"
        dataSource={remoteBranches}
        renderItem={(branch) => (
          <List.Item
            key={branch.name}
            actions={[
              <Tooltip title={t('git.checkout') || ''} key="checkout">
                <Button
                  icon={<CheckOutlined />}
                  size="small"
                  onClick={() => handleCheckoutBranch(branch.name)}
                />
              </Tooltip>,
              <Tooltip title={t('git.pull') || ''} key="pull">
                <Button
                  icon={<CloudDownloadOutlined />}
                  size="small"
                  onClick={() => handlePullBranch(branch)}
                />
              </Tooltip>,
            ]}
          >
            <div className="git-branch-item">
              <BranchesOutlined />
              <span className="git-branch-name">{branch.name}</span>
              <Tag color="blue">{branch.remoteName}</Tag>
            </div>
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className="git-branch-panel">
      {/* 操作按钮 */}
      <div className="git-branch-actions">
        <Space>
          <Button
            icon={<PlusOutlined />}
            onClick={handleCreateBranch}
          >
            {t('git.createBranch') || ''}
          </Button>
          <Button
            icon={<MergeCellsOutlined />}
            onClick={handleMergeBranch}
          >
            {t('git.mergeBranch') || ''}
          </Button>
        </Space>
      </div>

      {/* 分支列表 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'local',
            label: (
              <span>
                <BranchesOutlined />
                {t('git.localBranches') || ''}
              </span>
            ),
            children: renderLocalBranches()
          },
          {
            key: 'remote',
            label: (
              <span>
                <CloudUploadOutlined />
                {t('git.remoteBranches') || ''}
              </span>
            ),
            children: renderRemoteBranches()
          }
        ]}
      />

      {/* 创建分支对话框 */}
      <Modal
        title={t('git.createBranch') || ''}
        open={isCreateBranchModalVisible}
        onOk={handleConfirmCreateBranch}
        onCancel={handleCancelCreateBranch}
        okText={t('git.create') || ''}
        cancelText={t('common.cancel') || ''}
       destroyOnClose keyboard={true} maskClosable={true}>
        <div className="git-branch-form">
          <p>{t('git.enterBranchName') || ''}</p>
          <Input
            value={newBranchName}
            onChange={(e) => setNewBranchName(e.target.value)}
            placeholder={t('git.branchNamePlaceholder') || ''}
            prefix={<BranchesOutlined />}
          />
        </div>
      </Modal>

      {/* 合并分支对话框 */}
      <Modal
        title={t('git.mergeBranch') || ''}
        open={isMergeBranchModalVisible}
        onOk={handleConfirmMergeBranch}
        onCancel={handleCancelMergeBranch}
        okText={t('git.merge') || ''}
        cancelText={t('common.cancel') || ''}
       destroyOnClose keyboard={true} maskClosable={true}>
        <div className="git-branch-form">
          <p>{t('git.selectBranchToMerge') || ''}</p>
          <Select
            style={{ width: '100%' }}
            placeholder={t('git.selectBranch') || ''}
            value={selectedBranchForMerge}
            onChange={setSelectedBranchForMerge}
          >
            {safeBranches
              .filter(branch => branch && branch.name && branch.name !== currentBranch)
              .map(branch => (
                <Option key={branch.name} value={branch.name}>
                  {branch.name} {branch.remote && `(${branch.remoteName || ''})`}
                </Option>
              ))}
          </Select>
        </div>
      </Modal>
    </div>
  );
};

export default GitBranchPanel;
