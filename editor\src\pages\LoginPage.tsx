/**
 * 登录页面
 */
import React, { useEffect } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Divider, message, Alert } from 'antd';
import { UserOutlined, LockOutlined, GithubOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { login, clearError } from '../store/auth/authSlice';

const { Title, Text } = Typography;

export const LoginPage: React.FC = () => {
  const { t } = useTranslation('auth'); // 使用默认命名空间
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const { isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth);
  
  // 如果已经登录，重定向到首页或来源页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from || '/projects';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);
  
  // 清除错误
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);
  
  // 处理登录
  const handleLogin = (values: { email: string; password: string; remember: boolean }) => {
    dispatch(login({ email: values.email, password: values.password, remember: values.remember }))
      .unwrap()
      .then(() => {
        message.success(t('loginSuccess'));
        // 如果选择记住我，设置更长的token过期时间
        if (values.remember) {
          // 设置记住我标志到localStorage
          localStorage.setItem('rememberMe', 'true');
        } else {
          localStorage.removeItem('rememberMe');
        }
      })
      .catch(() => {
        // 错误已经在状态中处理
      });
  };
  
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: '#f0f2f5'}}
    >
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0 }}>
            {t('loginTitle')}
          </Title>
          <Text type="secondary">{t('loginSubtitle')}</Text>
        </div>
        
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
            closable
            onClose={() => {
          try {
            dispatch(clearError())
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
          />
        )}
        
        <Form
          name="login"
          initialValues={{ remember: true }}
          onFinish={handleLogin}
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: t('emailRequired') || '请输入邮箱' },
              { type: 'email', message: t('emailInvalid') || '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('emailPlaceholder') || '请输入邮箱'}
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: t('passwordRequired') || '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('passwordPlaceholder') || '请输入密码'}
              size="large"
            />
          </Form.Item>
          
          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>{t('rememberMe')}</Checkbox>
              </Form.Item>
              <Link to="/forgot-password">{t('forgotPassword')}</Link>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              block
              loading={isLoading}
            >
              {t('login')}
            </Button>
          </Form.Item>

          <Form.Item style={{ textAlign: 'center', marginBottom: 0 }}>
            <Text>
              {t('noAccount')}{' '}
              <Link to="/register">{t('register')}</Link>
            </Text>
          </Form.Item>
        </Form>
        
        <Divider plain>{t('orLoginWith')}</Divider>
        
        <div style={{ display: 'flex', justifyContent: 'center', gap: 16 }}>
          <Button icon={<GithubOutlined />} size="large" shape="circle" />
          <Button icon={<GoogleOutlined />} size="large" shape="circle" />
          <Button icon={<FacebookOutlined />} size="large" shape="circle" />
        </div>
      </Card>
    </div>
  );
};
