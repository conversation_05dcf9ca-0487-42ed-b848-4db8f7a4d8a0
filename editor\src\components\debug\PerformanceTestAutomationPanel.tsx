/**
 * 性能测试自动化面板组件
 * 用于自动化执行性能测试并生成报告
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, Button, Table, Tabs, Empty, Progress, Space, Typography, Divider, Modal, Form, Input, InputNumber, Switch, List, Collapse, Statistic } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  PlayCircleOutlined,
  PauseOutlined,
  FileAddOutlined,
  DeleteOutlined,
  DownloadOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  SaveOutlined,
  SettingOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { PerformanceTestAutomationService, TestConfig, TestResult, PerformanceTestEventType, TestSceneConfig, PerformanceMetricType } from '../../services/PerformanceTestAutomationService';
import { PerformanceComparisonService } from '../../services/PerformanceComparisonService';
import './PerformanceTestAutomationPanel.less';

const { TabPane } = Tabs;
const { Title } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

// 格式化时间
const formatTime = (ms: number): string => {
  if (ms < 1000) {
    return `${ms.toFixed(0)} ms`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)} s`;
  } else {
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(0);
    return `${minutes}m ${seconds}s`;
  }
};

interface PerformanceTestAutomationPanelProps {
  className?: string;
}

/**
 * 性能测试自动化面板组件
 */
const PerformanceTestAutomationPanel: React.FC<PerformanceTestAutomationPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 状态
  const [predefinedTests, setPredefinedTests] = useState<Map<string, TestConfig>>(new Map());
  const [selectedTestId, setSelectedTestId] = useState<string>('');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isRunningTest, setIsRunningTest] = useState<boolean>(false);
  const [testProgress, setTestProgress] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>('predefined');
  const [isEditingTest, setIsEditingTest] = useState<boolean>(false);
  const [editingTestForm] = Form.useForm();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState<boolean>(false);
  const [testToDelete, setTestToDelete] = useState<string>('');
  const [isExportModalVisible, setIsExportModalVisible] = useState<boolean>(false);
  const [exportFormat, setExportFormat] = useState<string>('json');
  
  // 服务实例
  const testService = PerformanceTestAutomationService.getInstance();
  const comparisonService = PerformanceComparisonService.getInstance();
  
  // 加载预定义测试
  useEffect(() => {
    loadPredefinedTests();
    
    // 添加事件监听器
    testService.on(PerformanceTestEventType.TEST_STARTED, handleTestStarted);
    testService.on(PerformanceTestEventType.TEST_COMPLETED, handleTestCompleted);
    testService.on(PerformanceTestEventType.TEST_FAILED, handleTestFailed);
    testService.on(PerformanceTestEventType.TEST_PROGRESS, handleTestProgress);
    
    return () => {
      // 移除事件监听器
      testService.off(PerformanceTestEventType.TEST_STARTED, handleTestStarted);
      testService.off(PerformanceTestEventType.TEST_COMPLETED, handleTestCompleted);
      testService.off(PerformanceTestEventType.TEST_FAILED, handleTestFailed);
      testService.off(PerformanceTestEventType.TEST_PROGRESS, handleTestProgress);
    };
  }, []);
  
  // 加载预定义测试
  const loadPredefinedTests = () => {
    const tests = testService.getPredefinedTests();
    setPredefinedTests(tests);
    
    // 如果有测试，默认选择第一个
    if (tests.size > 0) {
      setSelectedTestId(Array.from(tests.keys())[0]);
    }
  };
  
  // 处理测试开始事件
  const handleTestStarted = () => {
    setIsRunningTest(true);
    setTestProgress(0);
    setTestResult(null);
  };
  
  // 处理测试完成事件
  const handleTestCompleted = (result: TestResult) => {
    setIsRunningTest(false);
    setTestProgress(100);
    setTestResult(result);
    setActiveTab('results');
  };
  
  // 处理测试失败事件
  const handleTestFailed = () => {
    setIsRunningTest(false);
  };
  
  // 处理测试进度事件
  const handleTestProgress = (data: { progress: number }) => {
    setTestProgress(data.progress * 100);
  };
  
  // 运行测试
  const runTest = () => {
    if (isRunningTest || !selectedTestId) {
      return;
    }
    
    const testConfig = predefinedTests.get(selectedTestId);
    if (testConfig) {
      testService.runTest(testConfig);
    }
  };
  
  // 停止测试
  const stopTest = () => {
    if (!isRunningTest) {
      return;
    }
    
    testService.stopTest();
  };
  
  // 创建新测试
  const createNewTest = () => {
    editingTestForm.resetFields();
    editingTestForm.setFieldsValue({
      name: '新性能测试',
      description: '',
      repetitions: 3,
      autoSaveReport: true,
      scenes: [
        {
          id: `scene_${Date.now()}`,
          name: '测试场景',
          path: '',
          duration: 5000,
          warmupDuration: 1000
        }
      ]
    });
    setIsEditingTest(true);
  };
  
  // 编辑测试
  const editTest = (testId: string) => {
    const testConfig = predefinedTests.get(testId);
    if (testConfig) {
      editingTestForm.resetFields();
      editingTestForm.setFieldsValue(testConfig);
      setIsEditingTest(true);
    }
  };
  
  // 处理测试编辑提交
  const handleTestEditSubmit = (values: any) => {
    const testId = values.id || `test_${Date.now()}`;
    testService.addPredefinedTest(testId, values);
    setIsEditingTest(false);
    loadPredefinedTests();
    setSelectedTestId(testId);
  };
  
  // 删除测试
  const confirmDeleteTest = (testId: string) => {
    setTestToDelete(testId);
    setIsDeleteModalVisible(true);
  };
  
  // 处理测试删除确认
  const handleDeleteConfirm = () => {
    if (testToDelete) {
      testService.deletePredefinedTest(testToDelete);
      if (selectedTestId === testToDelete) {
        setSelectedTestId('');
      }
      setTestToDelete('');
      loadPredefinedTests();
    }
    setIsDeleteModalVisible(false);
  };
  
  // 导出测试结果
  const exportTestResult = () => {
    if (!testResult) {
      return;
    }
    
    setIsExportModalVisible(true);
  };
  
  // 处理导出确认
  const handleExportConfirm = () => {
    if (!testResult) {
      return;
    }
    
    // 导出为JSON
    if (exportFormat === 'json') {
      const json = JSON.stringify(testResult, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-test-result-${new Date().toISOString().replace(/:/g, '-')}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
    // 导出为HTML报告
    else if (exportFormat === 'html') {
      // 这里应该调用生成HTML报告的函数
      // 暂时使用简单的HTML模板
      const html = generateHtmlReport(testResult);
      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-test-report-${new Date().toISOString().replace(/:/g, '-')}.html`;
      a.click();
      URL.revokeObjectURL(url);
    }
    
    setIsExportModalVisible(false);
  };
  
  // 生成HTML报告
  const generateHtmlReport = (result: TestResult): string => {
    // 简单的HTML报告模板
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>性能测试报告 - ${result.config.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1, h2, h3 { color: #333; }
          table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .metric-good { color: green; }
          .metric-bad { color: red; }
          .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h1>性能测试报告</h1>
        <div class="summary">
          <h2>${result.config.name}</h2>
          <p>${result.config.description || ''}</p>
          <p>测试开始时间: ${new Date(result.startTime).toLocaleString()}</p>
          <p>测试结束时间: ${new Date(result.endTime).toLocaleString()}</p>
          <p>总持续时间: ${formatTime(result.totalDuration)}</p>
          <p>重复次数: ${result.config.repetitions}</p>
        </div>
        
        <h2>场景测试结果</h2>
        ${result.sceneResults.map(sceneResult => `
          <h3>${sceneResult.sceneConfig.name}</h3>
          <table>
            <tr>
              <th>指标</th>
              <th>平均值</th>
              <th>最小值</th>
              <th>最大值</th>
            </tr>
            ${Object.entries(sceneResult.averageReport.metrics).map(([key, metric]) => `
              <tr>
                <td>${key}</td>
                <td>${metric.value.toFixed(2)} ${metric.unit || ''}</td>
                <td>${metric.min.toFixed(2)} ${metric.unit || ''}</td>
                <td>${metric.max.toFixed(2)} ${metric.unit || ''}</td>
              </tr>
            `).join('')}
          </table>
          
          <h4>性能瓶颈</h4>
          ${sceneResult.averageReport.bottlenecks.length > 0 ? `
            <ul>
              ${sceneResult.averageReport.bottlenecks.map(bottleneck => `
                <li>
                  <strong>${bottleneck.type}</strong> (严重程度: ${(bottleneck.severity * 100).toFixed(0)}%)<br>
                  ${bottleneck.description}<br>
                  优化建议:
                  <ul>
                    ${bottleneck.optimizationSuggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                  </ul>
                </li>
              `).join('')}
            </ul>
          ` : '<p>未检测到性能瓶颈</p>'}
        `).join('')}
      </body>
      </html>
    `;
  };
  
  // 保存为性能快照
  const saveAsSnapshot = () => {
    if (!testResult) {
      return;
    }
    
    // 为每个场景创建一个快照
    testResult.sceneResults.forEach(sceneResult => {
      comparisonService.createSnapshot(
        `${testResult.config.name} - ${sceneResult.sceneConfig.name}`,
        sceneResult.averageReport as any, // 使用类型断言解决接口不兼容问题
        `自动化测试结果 - ${new Date().toLocaleString()}`,
        ['自动化测试']
      );
    });
    
    // 显示成功消息
    Modal.success({
      title: t('debug.performance.test.snapshotCreated'),
      content: t('debug.performance.test.snapshotCreatedMessage')
    });
  };
  
  // 渲染测试选择器
  const renderTestSelector = () => {
    return (
      <div className="test-selector">
        <Row gutter={16} align="middle">
          <Col span={16}>
            <Select
              placeholder={t('debug.performance.test.selectTest')}
              style={{ width: '100%' }}
              value={selectedTestId || undefined}
              onChange={setSelectedTestId}
            >
              {Array.from(predefinedTests.entries()).map(([id, config]) => (
                <Option key={id} value={id}>
                  {config.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Space>
              <Button
                type="primary"
                icon={isRunningTest ? <PauseOutlined /> : <PlayCircleOutlined />}
                onClick={isRunningTest ? stopTest : runTest}
                disabled={!selectedTestId}
              >
                {isRunningTest ? t('debug.performance.test.stopTest') : t('debug.performance.test.runTest')}
              </Button>
              <Button
                icon={<FileAddOutlined />}
                onClick={createNewTest}
              >
                {t('debug.performance.test.newTest')}
              </Button>
            </Space>
          </Col>
        </Row>
        
        {isRunningTest && (
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Progress percent={Math.round(testProgress)} status="active" />
            </Col>
          </Row>
        )}
      </div>
    );
  };
  
  // 渲染预定义测试标签页
  const renderPredefinedTestsTab = () => {
    if (predefinedTests.size === 0) {
      return (
        <Empty
          description={t('debug.performance.test.noTests')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={createNewTest}>
            {t('debug.performance.test.createTest')}
          </Button>
        </Empty>
      );
    }
    
    const columns = [
      {
        title: t('debug.performance.test.name'),
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: t('debug.performance.test.description'),
        dataIndex: 'description',
        key: 'description',
        ellipsis: true
      },
      {
        title: t('debug.performance.test.scenes'),
        dataIndex: 'scenes',
        key: 'scenes',
        render: (scenes: TestSceneConfig[]) => scenes.length
      },
      {
        title: t('debug.performance.test.repetitions'),
        dataIndex: 'repetitions',
        key: 'repetitions'
      },
      {
        title: t('debug.performance.test.actions'),
        key: 'actions',
        render: (_text: string, _record: TestConfig, index: number) => (
          <Space>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => editTest(Array.from(predefinedTests.keys())[index])}
            />
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => confirmDeleteTest(Array.from(predefinedTests.keys())[index])}
            />
          </Space>
        )
      }
    ];
    
    return (
      <div className="predefined-tests">
        <Table
          columns={columns}
          dataSource={Array.from(predefinedTests.values())}
          rowKey="name"
          pagination={false}
        />
      </div>
    );
  };
  
  // 渲染测试结果标签页
  const renderResultsTab = () => {
    if (!testResult) {
      return (
        <Empty
          description={t('debug.performance.test.noResults')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    return (
      <div className="test-results">
        <div className="results-header">
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={exportTestResult}
            >
              {t('debug.performance.test.exportResults')}
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={saveAsSnapshot}
            >
              {t('debug.performance.test.saveAsSnapshot')}
            </Button>
          </Space>
        </div>
        
        <Card className="test-summary">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title={t('debug.performance.test.testName')}
                value={testResult.config.name}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={t('debug.performance.test.totalDuration')}
                value={formatTime(testResult.totalDuration)}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={t('debug.performance.test.scenesTested')}
                value={testResult.sceneResults.length}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
          </Row>
        </Card>
        
        <Divider>{t('debug.performance.test.sceneResults')}</Divider>
        
        <Collapse defaultActiveKey={['0']}>
          {testResult.sceneResults.map((sceneResult, index) => (
            <Panel
              header={`${sceneResult.sceneConfig.name} (${formatTime(sceneResult.sceneConfig.duration)})`}
              key={index.toString()}
            >
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title={t('debug.performance.fps')}
                      value={sceneResult.averageReport.metrics[PerformanceMetricType.FPS]?.value || 0}
                      precision={1}
                      suffix="FPS"
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title={t('debug.performance.memory')}
                      value={sceneResult.averageReport.metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0}
                      precision={1}
                      suffix="MB"
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title={t('debug.performance.renderTime')}
                      value={sceneResult.averageReport.metrics[PerformanceMetricType.RENDER_TIME]?.value || 0}
                      precision={1}
                      suffix="ms"
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title={t('debug.performance.drawCalls')}
                      value={sceneResult.averageReport.metrics[PerformanceMetricType.DRAW_CALLS]?.value || 0}
                      precision={0}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Title level={5} style={{ marginTop: 16 }}>{t('debug.performance.bottlenecks')}</Title>
              {sceneResult.averageReport.bottlenecks.length > 0 ? (
                <List
                  dataSource={sceneResult.averageReport.bottlenecks}
                  renderItem={bottleneck => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<WarningOutlined style={{ color: '#faad14', fontSize: 20 }} />}
                        title={`${t(`debug.performance.bottleneck.${bottleneck.type}`)} (${(bottleneck.severity * 100).toFixed(0)}%)`}
                        description={bottleneck.description}
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty
                  description={t('debug.performance.test.noBottlenecks')}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };
  
  return (
    <div className={`performance-test-automation-panel ${className || ''}`}>
      {renderTestSelector()}
      
      <Divider />
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('debug.performance.test.predefinedTests')} key="predefined">
          {renderPredefinedTestsTab()}
        </TabPane>
        <TabPane tab={t('debug.performance.test.results')} key="results">
          {renderResultsTab()}
        </TabPane>
      </Tabs>
      
      {/* 编辑测试对话框 */}
      <Modal
        title={t('debug.performance.test.editTest')}
        open={isEditingTest}
        onOk={() = destroyOnClose keyboard={true} maskClosable={true}> editingTestForm.submit()}
        onCancel={() => {
          try {
            setIsEditingTest(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        width={800}
      >
        <Form
          form={editingTestForm}
          layout="vertical"
          onFinish={handleTestEditSubmit}
        >
          <Form.Item
            name="name"
            label={t('debug.performance.test.testName')}
            rules={[{ required: true, message: t('debug.performance.test.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('debug.performance.test.testDescription')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="repetitions"
            label={t('debug.performance.test.repetitions')}
            rules={[{ required: true, message: t('debug.performance.test.repetitionsRequired') as string }]}
          >
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="autoSaveReport"
            label={t('debug.performance.test.autoSaveReport')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Divider>{t('debug.performance.test.scenes')}</Divider>
          
          <Form.List name="scenes">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <div key={key} style={{ marginBottom: 16, border: '1px dashed #d9d9d9', padding: 16, borderRadius: 4 }}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'name']}
                          label={t('debug.performance.test.sceneName')}
                          rules={[{ required: true, message: t('debug.performance.test.sceneNameRequired') as string }]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'path']}
                          label={t('debug.performance.test.scenePath')}
                          rules={[{ required: true, message: t('debug.performance.test.scenePathRequired') as string }]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'duration']}
                          label={t('debug.performance.test.duration')}
                          rules={[{ required: true, message: t('debug.performance.test.durationRequired') as string }]}
                        >
                          <InputNumber min={1000} step={1000} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'warmupDuration']}
                          label={t('debug.performance.test.warmupDuration')}
                          rules={[{ required: true, message: t('debug.performance.test.warmupDurationRequired') as string }]}
                        >
                          <InputNumber min={0} step={1000} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                    
                    <Button
                      type="dashed"
                      danger
                      onClick={() => remove(name)}
                      icon={<MinusCircleOutlined />}
                      style={{ marginTop: 8 }}
                      disabled={fields.length <= 1}
                    >
                      {t('debug.performance.test.removeScene')}
                    </Button>
                  </div>
                ))}
                
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add({
                      id: `scene_${Date.now()}`,
                      name: '新场景',
                      path: '',
                      duration: 5000,
                      warmupDuration: 1000
                    })}
                    icon={<PlusOutlined />}
                    style={{ width: '100%' }}
                  >
                    {t('debug.performance.test.addScene')}
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
      
      {/* 删除测试确认对话框 */}
      <Modal
        title={t('debug.performance.test.confirmDelete')}
        open={isDeleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsDeleteModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <p>{t('debug.performance.test.deleteWarning')}</p>
      </Modal>
      
      {/* 导出结果对话框 */}
      <Modal
        title={t('debug.performance.test.exportResults')}
        open={isExportModalVisible}
        onOk={handleExportConfirm}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsExportModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('debug.performance.test.exportFormat')}
          >
            <Select
              value={exportFormat}
              onChange={setExportFormat}
              style={{ width: '100%' }}
            >
              <Option value="json">JSON</Option>
              <Option value="html">HTML</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PerformanceTestAutomationPanel;
