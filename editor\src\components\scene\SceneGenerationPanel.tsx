/**
 * 场景生成管理面板
 * 整合文本和语音场景生成功能的主面板
 */
import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Statistic,
  Row,
  Col,
  Timeline,
  Tag,
  Tooltip,
  Modal,
  List,
  Avatar,
  Input,
  Select,

  Slider,
  Checkbox,
  message,
  Divider,
  Empty
} from 'antd';
import {
  AudioOutlined,
  FileTextOutlined,
  HistoryOutlined,
  SettingOutlined,
  ExportOutlined,
  EyeOutlined,
  DeleteOutlined,
  StarOutlined,
  SearchOutlined,
  ReloadOutlined,
  FilterOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import VoiceSceneGenerationPanel from './VoiceSceneGenerationPanel';
import TextSceneGenerationPanel from './TextSceneGenerationPanel';

const { Title, Text } = Typography;

/**
 * 生成历史记录
 */
interface GenerationHistory {
  id: string;
  type: 'voice' | 'text';
  timestamp: number;
  description: string;
  scene: any;
  confidence: number;
  duration: number;
  favorite: boolean;
  regeneratedAt?: number;
  regenerationCount?: number;
  tags?: string[];
  serverMetadata?: any;
}

/**
 * 场景生成统计
 */
interface GenerationStats {
  totalGenerated: number;
  voiceGenerated: number;
  textGenerated: number;
  averageConfidence: number;
  averageDuration: number;
  favoriteCount: number;
}

/**
 * 场景生成面板属性
 */
interface SceneGenerationPanelProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 场景生成完成回调 */
  onSceneGenerated?: (scene: any, type: 'voice' | 'text') => void;
  /** 错误回调 */
  onError?: (error: string) => void;
}

/**
 * 场景生成管理面板
 */
const SceneGenerationPanel: React.FC<SceneGenerationPanelProps> = ({
  disabled = false,
  onSceneGenerated,
  onError
}) => {

  // 状态管理
  const [activeTab, setActiveTab] = useState<string>('text');
  const [generationHistory, setGenerationHistory] = useState<GenerationHistory[]>([]);
  const [stats, setStats] = useState<GenerationStats>({
    totalGenerated: 0,
    voiceGenerated: 0,
    textGenerated: 0,
    averageConfidence: 0,
    averageDuration: 0,
    favoriteCount: 0
  });
  const [showHistory, setShowHistory] = useState(false);
  const [showStats, setShowStats] = useState(false);

  // 新增状态：批量操作
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [batchMode, setBatchMode] = useState(false);

  // 新增状态：搜索过滤
  const [searchFilters, setSearchFilters] = useState({
    keyword: '',
    type: 'all' as 'all' | 'voice' | 'text',
    dateRange: null as [any, any] | null,
    confidenceRange: [0, 1] as [number, number],
    favoriteOnly: false,
    sortBy: 'timestamp' as 'timestamp' | 'confidence' | 'description',
    sortOrder: 'desc' as 'asc' | 'desc'
  });

  // 新增状态：高级功能
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showQualityAnalytics, setShowQualityAnalytics] = useState(false);

  // 组件引用
  const textPanelRef = useRef<any>(null);
  const voicePanelRef = useRef<any>(null);

  /**
   * 过滤和排序历史记录
   */
  const getFilteredHistory = useMemo(() => {
    let filtered = [...generationHistory];

    // 关键词搜索
    if (searchFilters.keyword) {
      const keyword = searchFilters.keyword.toLowerCase();
      filtered = filtered.filter(item =>
        item.description.toLowerCase().includes(keyword) ||
        item.id.toLowerCase().includes(keyword)
      );
    }

    // 类型过滤
    if (searchFilters.type !== 'all') {
      filtered = filtered.filter(item => item.type === searchFilters.type);
    }

    // 置信度范围过滤
    filtered = filtered.filter(item =>
      item.confidence >= searchFilters.confidenceRange[0] &&
      item.confidence <= searchFilters.confidenceRange[1]
    );

    // 收藏过滤
    if (searchFilters.favoriteOnly) {
      filtered = filtered.filter(item => item.favorite);
    }

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (searchFilters.sortBy) {
        case 'timestamp':
          comparison = a.timestamp - b.timestamp;
          break;
        case 'confidence':
          comparison = a.confidence - b.confidence;
          break;
        case 'description':
          comparison = a.description.localeCompare(b.description);
          break;
      }
      return searchFilters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [generationHistory, searchFilters]);

  /**
   * 初始化数据
   */
  useEffect(() => {
    loadGenerationHistory();
  }, []);

  /**
   * 当历史记录变化时重新计算统计数据
   */
  useEffect(() => {
    calculateStats();
  }, [generationHistory]);

  /**
   * 加载生成历史
   */
  const loadGenerationHistory = () => {
    // 从本地存储加载历史记录
    const savedHistory = localStorage.getItem('sceneGenerationHistory');
    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory);
        setGenerationHistory(history);
      } catch (error) {
        console.error('加载历史记录失败:', error);
      }
    }
  };

  /**
   * 保存生成历史
   */
  const saveGenerationHistory = (history: GenerationHistory[]) => {
    try {
      localStorage.setItem('sceneGenerationHistory', JSON.stringify(history));
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  /**
   * 计算统计数据
   */
  const calculateStats = () => {
    if (generationHistory.length === 0) {
      setStats({
        totalGenerated: 0,
        voiceGenerated: 0,
        textGenerated: 0,
        averageConfidence: 0,
        averageDuration: 0,
        favoriteCount: 0
      });
      return;
    }

    const voiceCount = generationHistory.filter(h => h.type === 'voice').length;
    const textCount = generationHistory.filter(h => h.type === 'text').length;
    const favoriteCount = generationHistory.filter(h => h.favorite).length;

    const totalConfidence = generationHistory.reduce((sum, h) => sum + h.confidence, 0);
    const totalDuration = generationHistory.reduce((sum, h) => sum + h.duration, 0);

    setStats({
      totalGenerated: generationHistory.length,
      voiceGenerated: voiceCount,
      textGenerated: textCount,
      averageConfidence: totalConfidence / generationHistory.length,
      averageDuration: totalDuration / generationHistory.length,
      favoriteCount
    });
  };

  /**
   * 处理场景生成完成
   */
  const handleSceneGenerated = (scene: any, type: 'voice' | 'text') => {
    const newHistory: GenerationHistory = {
      id: `gen_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type,
      timestamp: Date.now(),
      description: scene.description || '',
      scene,
      confidence: scene.confidence || 0.8,
      duration: scene.generationTime || 3000,
      favorite: false
    };

    const updatedHistory = [newHistory, ...generationHistory].slice(0, 50); // 保留最近50条
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);

    // 回调
    onSceneGenerated?.(scene, type);
  };

  /**
   * 处理语音场景生成
   */
  const handleVoiceSceneGenerated = (scene: any) => {
    handleSceneGenerated(scene, 'voice');
  };

  /**
   * 处理文本场景生成
   */
  const handleTextSceneGenerated = (scene: any) => {
    handleSceneGenerated(scene, 'text');
  };

  /**
   * 切换收藏状态
   */
  const toggleFavorite = (id: string) => {
    const updatedHistory = generationHistory.map(item =>
      item.id === id ? { ...item, favorite: !item.favorite } : item
    );
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
  };

  /**
   * 删除历史记录
   */
  const deleteHistoryItem = (id: string) => {
    const updatedHistory = generationHistory.filter(item => item.id !== id);
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
  };

  /**
   * 重新生成场景 - 改进版本
   */
  const regenerateScene = async (historyItem: GenerationHistory) => {
    try {
      // 切换到对应的标签页
      setActiveTab(historyItem.type);

      // 等待标签页切换完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 根据类型触发对应面板的重新生成
      if (historyItem.type === 'text') {
        // 触发文本面板的重新生成
        triggerTextRegeneration(historyItem.description);
      } else if (historyItem.type === 'voice') {
        // 触发语音面板的重新生成
        triggerVoiceRegeneration(historyItem.description);
      }

      // 添加重新生成标记到历史记录
      const updatedHistory = generationHistory.map(item =>
        item.id === historyItem.id
          ? {
              ...item,
              regeneratedAt: Date.now(),
              regenerationCount: (item.regenerationCount || 0) + 1
            }
          : item
      );
      setGenerationHistory(updatedHistory);
      saveGenerationHistory(updatedHistory);

      message.success('已触发重新生成');

    } catch (error) {
      console.error('重新生成失败:', error);
      onError?.('重新生成场景失败，请稍后重试');
    }
  };

  /**
   * 触发文本面板重新生成
   */
  const triggerTextRegeneration = (description: string) => {
    // 通过 ref 或事件系统触发文本面板重新生成
    if (textPanelRef.current?.regenerateWithDescription) {
      textPanelRef.current.regenerateWithDescription(description);
    } else {
      // 如果没有ref方法，显示提示
      Modal.info({
        title: '重新生成',
        content: `请在文本生成面板中输入以下描述进行重新生成：\n"${description}"`,
      });
    }
  };

  /**
   * 触发语音面板重新生成
   */
  const triggerVoiceRegeneration = (description: string) => {
    // 通过 ref 或事件系统触发语音面板重新生成
    if (voicePanelRef.current?.regenerateWithDescription) {
      voicePanelRef.current.regenerateWithDescription(description);
    } else {
      // 如果没有ref方法，显示提示
      Modal.info({
        title: '重新生成',
        content: `请在语音生成面板中说出以下内容进行重新生成：\n"${description}"`,
      });
    }
  };

  /**
   * 导出场景
   */
  const exportScene = (historyItem: GenerationHistory) => {
    const dataStr = JSON.stringify(historyItem.scene, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `scene_${historyItem.id}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  /**
   * 批量删除历史记录
   */
  const batchDeleteItems = () => {
    Modal.confirm({
      title: '批量删除确认',
      content: `确定要删除选中的 ${selectedItems.length} 条记录吗？此操作不可撤销。`,
      onOk: () => {
        const updatedHistory = generationHistory.filter(
          item => !selectedItems.includes(item.id)
        );
        setGenerationHistory(updatedHistory);
        saveGenerationHistory(updatedHistory);
        setSelectedItems([]);
        setBatchMode(false);
        message.success(`已删除 ${selectedItems.length} 条记录`);
      }
    });
  };

  /**
   * 批量导出场景
   */
  const batchExportScenes = () => {
    const selectedScenes = generationHistory.filter(
      item => selectedItems.includes(item.id)
    );

    const exportData = {
      exportTime: new Date().toISOString(),
      totalCount: selectedScenes.length,
      scenes: selectedScenes
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `scenes_batch_${Date.now()}.json`;
    link.click();

    URL.revokeObjectURL(url);
    message.success(`已导出 ${selectedScenes.length} 个场景`);
  };

  /**
   * 批量设置收藏状态
   */
  const batchToggleFavorite = (favorite: boolean) => {
    const updatedHistory = generationHistory.map(item =>
      selectedItems.includes(item.id) ? { ...item, favorite } : item
    );
    setGenerationHistory(updatedHistory);
    saveGenerationHistory(updatedHistory);
    setSelectedItems([]);
    setBatchMode(false);
    message.success(`已${favorite ? '收藏' : '取消收藏'} ${selectedItems.length} 条记录`);
  };

  /**
   * 高级搜索面板
   */
  const renderAdvancedSearch = () => (
    <Card size="small" title="高级搜索" style={{ marginBottom: '16px' }}>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Input
            placeholder="搜索描述或ID"
            prefix={<SearchOutlined />}
            value={searchFilters.keyword}
            onChange={(e) => setSearchFilters(prev => ({ ...prev, keyword: e.target.value }))}
            allowClear
          />
        </Col>
        <Col span={4}>
          <Select
            value={searchFilters.type}
            onChange={(value) => setSearchFilters(prev => ({ ...prev, type: value }))}
            style={{ width: '100%' }}
          >
            <Select.Option value="all">全部类型</Select.Option>
            <Select.Option value="text">文本生成</Select.Option>
            <Select.Option value="voice">语音生成</Select.Option>
          </Select>
        </Col>
        <Col span={6}>
          <Space>
            <span>置信度:</span>
            <Slider
              range
              min={0}
              max={1}
              step={0.1}
              value={searchFilters.confidenceRange}
              onChange={(value) => setSearchFilters(prev => ({ ...prev, confidenceRange: value as [number, number] }))}
              style={{ width: '120px' }}
            />
          </Space>
        </Col>
        <Col span={6}>
          <Space>
            <Checkbox
              checked={searchFilters.favoriteOnly}
              onChange={(e) => setSearchFilters(prev => ({ ...prev, favoriteOnly: e.target.checked }))}
            >
              仅显示收藏
            </Checkbox>
          </Space>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={8}>
          <Space>
            <span>排序:</span>
            <Select
              value={searchFilters.sortBy}
              onChange={(value) => setSearchFilters(prev => ({ ...prev, sortBy: value }))}
              style={{ width: '120px' }}
            >
              <Select.Option value="timestamp">时间</Select.Option>
              <Select.Option value="confidence">置信度</Select.Option>
              <Select.Option value="description">描述</Select.Option>
            </Select>
            <Select
              value={searchFilters.sortOrder}
              onChange={(value) => setSearchFilters(prev => ({ ...prev, sortOrder: value }))}
              style={{ width: '80px' }}
            >
              <Select.Option value="desc">降序</Select.Option>
              <Select.Option value="asc">升序</Select.Option>
            </Select>
          </Space>
        </Col>
        <Col span={4}>
          <Button
            onClick={() => setSearchFilters({
              keyword: '',
              type: 'all',
              dateRange: null,
              confidenceRange: [0, 1],
              favoriteOnly: false,
              sortBy: 'timestamp',
              sortOrder: 'desc'
            })}
          >
            重置筛选
          </Button>
        </Col>
      </Row>
    </Card>
  );

  /**
   * 批量操作工具栏
   */
  const renderBatchToolbar = () => (
    <div className="batch-toolbar" style={{
      padding: '8px 16px',
      backgroundColor: '#f0f2f5',
      borderRadius: '6px',
      marginBottom: '16px'
    }}>
      <Row justify="space-between" align="middle">
        <Col>
          <Space>
            <span>已选择 {selectedItems.length} 项</span>
            <Button size="small" onClick={() => setSelectedItems([])}>
              清空选择
            </Button>
          </Space>
        </Col>
        <Col>
          <Space>
            <Button
              size="small"
              icon={<StarOutlined />}
              onClick={() => batchToggleFavorite(true)}
              disabled={selectedItems.length === 0}
            >
              批量收藏
            </Button>
            <Button
              size="small"
              icon={<ExportOutlined />}
              onClick={batchExportScenes}
              disabled={selectedItems.length === 0}
            >
              批量导出
            </Button>
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={batchDeleteItems}
              disabled={selectedItems.length === 0}
            >
              批量删除
            </Button>
          </Space>
        </Col>
      </Row>
    </div>
  );

  /**
   * 生成质量分析组件
   */
  const renderQualityAnalytics = () => {
    const analytics = useMemo(() => {
      const totalScenes = generationHistory.length;
      if (totalScenes === 0) return null;

      const qualityDistribution = {
        high: generationHistory.filter(item => item.confidence > 0.8).length,
        medium: generationHistory.filter(item => item.confidence > 0.6 && item.confidence <= 0.8).length,
        low: generationHistory.filter(item => item.confidence <= 0.6).length
      };

      const typePerformance = {
        voice: {
          count: generationHistory.filter(item => item.type === 'voice').length,
          avgConfidence: generationHistory
            .filter(item => item.type === 'voice')
            .reduce((sum, item) => sum + item.confidence, 0) /
            (generationHistory.filter(item => item.type === 'voice').length || 1)
        },
        text: {
          count: generationHistory.filter(item => item.type === 'text').length,
          avgConfidence: generationHistory
            .filter(item => item.type === 'text')
            .reduce((sum, item) => sum + item.confidence, 0) /
            (generationHistory.filter(item => item.type === 'text').length || 1)
        }
      };

      const improvementSuggestions = [];
      if (qualityDistribution.low > qualityDistribution.high) {
        improvementSuggestions.push('尝试提供更详细和具体的场景描述');
      }
      if (typePerformance.voice.avgConfidence < 0.6) {
        improvementSuggestions.push('语音输入时请确保环境安静，发音清晰');
      }
      if (typePerformance.text.avgConfidence < 0.6) {
        improvementSuggestions.push('文本描述建议包含更多场景细节和上下文信息');
      }

      return {
        qualityDistribution,
        typePerformance,
        totalScenes,
        improvementSuggestions
      };
    }, [generationHistory]);

    if (!analytics) return null;

    return (
      <Card title="质量分析" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="高质量场景比例"
              value={(analytics.qualityDistribution.high / analytics.totalScenes * 100).toFixed(1)}
              suffix="%"
              valueStyle={{ color: analytics.qualityDistribution.high / analytics.totalScenes > 0.7 ? '#3f8600' : '#cf1322' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="最佳生成方式"
              value={analytics.typePerformance.voice.avgConfidence > analytics.typePerformance.text.avgConfidence ? '语音' : '文本'}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
        </Row>

        {analytics.improvementSuggestions.length > 0 && (
          <Alert
            message="改进建议"
            description={
              <ul>
                {analytics.improvementSuggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            }
            type="info"
            style={{ marginTop: 16 }}
          />
        )}
      </Card>
    );
  };

  /**
   * 渲染统计信息
   */
  const renderStats = () => (
    <Row gutter={16} style={{ marginBottom: '20px' }}>
      <Col span={6}>
        <Statistic
          title="总生成数"
          value={stats.totalGenerated}
          prefix={<EyeOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="语音生成"
          value={stats.voiceGenerated}
          prefix={<AudioOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="文本生成"
          value={stats.textGenerated}
          prefix={<FileTextOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title="平均置信度"
          value={stats.averageConfidence}
          precision={2}
          suffix="%"
          formatter={(value) => `${((value as number) * 100).toFixed(1)}%`}
        />
      </Col>
    </Row>
  );

  /**
   * 渲染历史记录
   */
  const renderHistory = () => {
    const displayHistory = getFilteredHistory.slice(0, 20); // 显示过滤后的前20条

    return (
      <div>
        {/* 高级搜索面板 */}
        {showAdvancedSearch && renderAdvancedSearch()}

        {/* 质量分析面板 */}
        {showQualityAnalytics && renderQualityAnalytics()}

        {/* 批量操作工具栏 */}
        {batchMode && selectedItems.length > 0 && renderBatchToolbar()}

        {/* 操作按钮 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
            >
              {showAdvancedSearch ? '隐藏' : '显示'}高级搜索
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={() => setShowQualityAnalytics(!showQualityAnalytics)}
            >
              {showQualityAnalytics ? '隐藏' : '显示'}质量分析
            </Button>
            <Button
              onClick={() => setBatchMode(!batchMode)}
            >
              {batchMode ? '退出' : '进入'}批量模式
            </Button>
          </Space>
        </div>

        <List
          dataSource={displayHistory}
          renderItem={(item) => (
            <List.Item
              actions={[
                ...(batchMode ? [
                  <Checkbox
                    key="select"
                    checked={selectedItems.includes(item.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems([...selectedItems, item.id]);
                      } else {
                        setSelectedItems(selectedItems.filter(id => id !== item.id));
                      }
                    }}
                  />
                ] : []),
                <Tooltip key="favorite" title={item.favorite ? '取消收藏' : '收藏'}>
                  <Button
                    type="text"
                    icon={<StarOutlined />}
                    style={{ color: item.favorite ? '#faad14' : undefined }}
                    onClick={() => toggleFavorite(item.id)}
                  />
                </Tooltip>,
                <Tooltip key="regenerate" title="重新生成">
                  <Button
                    type="text"
                    icon={<ReloadOutlined />}
                    onClick={() => regenerateScene(item)}
                  />
                </Tooltip>,
                <Tooltip key="export" title="导出">
                  <Button
                    type="text"
                    icon={<ExportOutlined />}
                    onClick={() => exportScene(item)}
                  />
                </Tooltip>,
                <Tooltip key="delete" title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => deleteHistoryItem(item.id)}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={item.type === 'voice' ? <AudioOutlined /> : <FileTextOutlined />}
                    style={{ backgroundColor: item.type === 'voice' ? '#52c41a' : '#1890ff' }}
                  />
                }
                title={
                  <div>
                    <Text strong>{item.description.substring(0, 50)}...</Text>
                    <Tag
                      color={item.confidence > 0.8 ? 'green' : item.confidence > 0.6 ? 'orange' : 'red'}
                      style={{ marginLeft: '8px' }}
                    >
                      {Math.round(item.confidence * 100)}%
                    </Tag>
                    {item.favorite && <StarOutlined style={{ color: '#faad14', marginLeft: '8px' }} />}
                    {item.regenerationCount && item.regenerationCount > 0 && (
                      <Tag color="blue" style={{ marginLeft: '8px' }}>
                        重生成 {item.regenerationCount} 次
                      </Tag>
                    )}
                  </div>
                }
                description={
                  <div>
                    <Text type="secondary">{new Date(item.timestamp).toLocaleString()}</Text>
                    <br />
                    <Text type="secondary">生成时间: {item.duration}ms</Text>
                    {item.regeneratedAt && (
                      <>
                        <br />
                        <Text type="secondary">最后重生成: {new Date(item.regeneratedAt).toLocaleString()}</Text>
                      </>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />

        {displayHistory.length === 0 && (
          <Empty description="没有找到匹配的历史记录" />
        )}
      </div>
    );
  };

  /**
   * 快捷操作面板
   */
  const renderQuickActions = () => {
    const recentTemplates = generationHistory
      .filter(item => item.favorite)
      .slice(0, 6);

    return (
      <Card title="快捷操作" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={[8, 8]}>
          {recentTemplates.map(template => (
            <Col span={8} key={template.id}>
              <Button
                size="small"
                block
                onClick={() => applyTemplate(template)}
                title={template.description}
              >
                {template.description.substring(0, 10)}...
              </Button>
            </Col>
          ))}
        </Row>
        <Divider style={{ margin: '12px 0' }} />
        <Space wrap>
          <Button size="small" icon={<HistoryOutlined />} onClick={() => setShowHistory(true)}>
            最近使用
          </Button>
          <Button size="small" icon={<StarOutlined />} onClick={() => {
            setSearchFilters(prev => ({ ...prev, favoriteOnly: true }));
            setShowHistory(true);
          }}>
            我的收藏
          </Button>
          <Button size="small" icon={<SettingOutlined />} onClick={() => setShowStats(true)}>
            统计信息
          </Button>
        </Space>
      </Card>
    );
  };

  /**
   * 应用模板
   */
  const applyTemplate = (template: GenerationHistory) => {
    setActiveTab(template.type);
    // 这里可以通过ref或其他方式将模板内容填充到对应的面板中
    message.success(`已应用模板: ${template.description.substring(0, 20)}...`);
  };

  /**
   * 导出所有历史记录
   */
  const exportAllHistory = () => {
    const exportData = {
      exportTime: new Date().toISOString(),
      totalCount: generationHistory.length,
      scenes: generationHistory
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `all_scenes_${Date.now()}.json`;
    link.click();

    URL.revokeObjectURL(url);
    message.success(`已导出 ${generationHistory.length} 个场景`);
  };

  /**
   * 渲染时间线
   */
  const renderTimeline = () => (
    <Timeline>
      {generationHistory.slice(0, 5).map((item) => (
        <Timeline.Item
          key={item.id}
          color={item.type === 'voice' ? 'green' : 'blue'}
          dot={item.type === 'voice' ? <AudioOutlined /> : <FileTextOutlined />}
        >
          <div>
            <Text strong>{item.description.substring(0, 30)}...</Text>
            <br />
            <Text type="secondary">{new Date(item.timestamp).toLocaleString()}</Text>
            <Tag color={item.confidence > 0.8 ? 'green' : item.confidence > 0.6 ? 'orange' : 'red'} style={{ marginLeft: '8px' }}>
              {Math.round(item.confidence * 100)}%
            </Tag>
          </div>
        </Timeline.Item>
      ))}
    </Timeline>
  );

  return (
    <div className="scene-generation-panel">
      {/* 快捷操作面板 */}
      {generationHistory.filter(item => item.favorite).length > 0 && renderQuickActions()}

      <Card
        title="智能场景生成"
        extra={
          <Space>
            <Tooltip title="查看统计">
              <Button
                icon={<SettingOutlined />}
                onClick={() => setShowStats(true)}
              />
            </Tooltip>
            <Tooltip title="查看历史">
              <Button
                icon={<HistoryOutlined />}
                onClick={() => setShowHistory(true)}
              />
            </Tooltip>
            <Tooltip title="导出历史">
              <Button
                icon={<ExportOutlined />}
                onClick={exportAllHistory}
              />
            </Tooltip>
          </Space>
        }
      >
        {/* 统计信息 */}
        {renderStats()}

        {/* 主要功能标签页 */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          items={[
            {
              key: 'text',
              label: (
                <span>
                  <FileTextOutlined />
                  文本生成
                </span>
              ),
              children: (
                <TextSceneGenerationPanel
                  disabled={disabled}
                  onSceneGenerated={handleTextSceneGenerated}
                  onError={onError}
                />
              )
            },
            {
              key: 'voice',
              label: (
                <span>
                  <AudioOutlined />
                  语音生成
                </span>
              ),
              children: (
                <VoiceSceneGenerationPanel
                  disabled={disabled}
                  onSceneGenerated={handleVoiceSceneGenerated}
                  onError={onError}
                />
              )
            },
            {
              key: 'recent',
              label: (
                <span>
                  <HistoryOutlined />
                  最近生成
                </span>
              ),
              children: (
                <div style={{ padding: '20px 0' }}>
                  <Title level={4}>最近生成的场景</Title>
                  {renderTimeline()}
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 统计信息模态框 */}
      <Modal
        title="生成统计"
        open={showStats}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setShowStats(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
        width={600}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="总生成数"
              value={stats.totalGenerated}
              prefix={<EyeOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="收藏数"
              value={stats.favoriteCount}
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="语音生成"
              value={stats.voiceGenerated}
              prefix={<AudioOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="文本生成"
              value={stats.textGenerated}
              prefix={<FileTextOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="平均置信度"
              value={stats.averageConfidence}
              precision={2}
              suffix="%"
              formatter={(value) => `${((value as number) * 100).toFixed(1)}%`}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="平均耗时"
              value={stats.averageDuration / 1000}
              precision={1}
              suffix="秒"
            />
          </Col>
        </Row>
      </Modal>

      {/* 历史记录模态框 */}
      <Modal
        title="生成历史"
        open={showHistory}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setShowHistory(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
        width={800}
      >
        {renderHistory()}
      </Modal>
    </div>
  );
};

export default SceneGenerationPanel;
