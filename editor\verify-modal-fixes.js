#!/usr/bin/env node

/**
 * 验证Modal关闭按钮修复的脚本
 * 检查所有Modal组件是否正确应用了修复
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归获取所有TypeScript/TSX文件
function getAllFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
          traverse(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 验证Modal组件的修复
function verifyModalFixes(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查是否有Modal组件
    const modalMatches = content.match(/<Modal[^>]*>/g);
    if (!modalMatches) {
      return { hasModal: false, issues: [] };
    }
    
    // 检查每个Modal组件
    modalMatches.forEach((modal, index) => {
      // 检查是否有destroyOnClose
      if (!modal.includes('destroyOnClose')) {
        issues.push(`Modal ${index + 1}: 缺少 destroyOnClose 属性`);
      }
      
      // 检查是否有keyboard
      if (!modal.includes('keyboard')) {
        issues.push(`Modal ${index + 1}: 缺少 keyboard 属性`);
      }
      
      // 检查是否有maskClosable
      if (!modal.includes('maskClosable')) {
        issues.push(`Modal ${index + 1}: 缺少 maskClosable 属性`);
      }
    });
    
    // 检查onCancel事件处理
    const onCancelMatches = content.match(/onCancel=\{[^}]+\}/g);
    if (onCancelMatches) {
      onCancelMatches.forEach((onCancel, index) => {
        // 检查是否有错误处理
        if (!onCancel.includes('try') && !onCancel.includes('catch')) {
          // 检查是否是简单的函数调用（这些可能已经在函数定义中有错误处理）
          if (onCancel.includes('=>') && !onCancel.includes('()')) {
            issues.push(`onCancel ${index + 1}: 可能缺少错误处理`);
          }
        }
      });
    }
    
    return { hasModal: true, issues };
  } catch (error) {
    return { hasModal: false, issues: [`读取文件失败: ${error.message}`] };
  }
}

// 检查useCallback的使用
function checkUseCallbackUsage(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 检查是否导入了useCallback
    const hasUseCallbackImport = content.includes('useCallback');
    
    // 查找事件处理函数定义
    const eventHandlerPattern = /const\s+(\w*(?:toggle|handle|on)\w*)\s*=\s*\(/g;
    const eventHandlers = [];
    let match;
    
    while ((match = eventHandlerPattern.exec(content)) !== null) {
      eventHandlers.push(match[1]);
    }
    
    if (eventHandlers.length > 0 && !hasUseCallbackImport) {
      issues.push('有事件处理函数但未导入 useCallback');
    }
    
    // 检查事件处理函数是否使用了useCallback
    eventHandlers.forEach(handler => {
      const handlerDefPattern = new RegExp(`const\\s+${handler}\\s*=\\s*useCallback`, 'g');
      if (!handlerDefPattern.test(content)) {
        issues.push(`事件处理函数 ${handler} 未使用 useCallback`);
      }
    });
    
    return issues;
  } catch (error) {
    return [`检查useCallback使用失败: ${error.message}`];
  }
}

// 主函数
function main() {
  console.log('🔍 开始验证Modal修复...');
  
  const srcDir = path.join(__dirname, 'src');
  if (!fs.existsSync(srcDir)) {
    console.error('❌ 找不到src目录');
    process.exit(1);
  }
  
  const files = getAllFiles(srcDir);
  console.log(`📁 检查 ${files.length} 个文件`);
  
  let totalModalFiles = 0;
  let totalIssues = 0;
  const problemFiles = [];
  
  for (const file of files) {
    const modalResult = verifyModalFixes(file);
    const callbackIssues = checkUseCallbackUsage(file);
    
    if (modalResult.hasModal) {
      totalModalFiles++;
      
      const allIssues = [...modalResult.issues, ...callbackIssues];
      if (allIssues.length > 0) {
        totalIssues += allIssues.length;
        problemFiles.push({
          file: path.relative(srcDir, file),
          issues: allIssues
        });
      }
    }
  }
  
  console.log(`\n📊 验证结果:`);
  console.log(`  - 包含Modal的文件: ${totalModalFiles}`);
  console.log(`  - 发现的问题: ${totalIssues}`);
  console.log(`  - 有问题的文件: ${problemFiles.length}`);
  
  if (problemFiles.length > 0) {
    console.log(`\n⚠️  需要注意的文件:`);
    problemFiles.forEach(({ file, issues }) => {
      console.log(`\n📄 ${file}:`);
      issues.forEach(issue => {
        console.log(`  - ${issue}`);
      });
    });
  } else {
    console.log(`\n✅ 所有Modal组件都已正确修复！`);
  }
  
  // 生成验证报告
  const report = {
    timestamp: new Date().toISOString(),
    totalFiles: files.length,
    modalFiles: totalModalFiles,
    totalIssues: totalIssues,
    problemFiles: problemFiles
  };
  
  fs.writeFileSync(
    path.join(__dirname, 'modal-verification-report.json'),
    JSON.stringify(report, null, 2),
    'utf8'
  );
  
  console.log(`\n📋 验证报告已保存到 modal-verification-report.json`);
}

// 运行验证
main();
