/**
 * 锁定面板组件
 * 显示当前的锁定状态和管理锁定
 */
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  Table,
  Button,
  Tag,
  Space,
  Modal,
  message,
  Switch,
  Select,
  Form,
  Divider,
  Badge,
  Card,
  Typography,
  Empty
} from 'antd';
import {
  UnlockOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { smartLockingService, Lock, LockStatus, LockPriority, LockType } from '../../services/SmartLockingService';
import './LockPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 锁定面板组件
 */
const LockPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 从Redux获取锁定状态
  const { locks, activeLocks, myLocks } = useSelector((state: RootState) => state.lock);
  
  // 本地状态
  const [selectedLockId, setSelectedLockId] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [showMyLocksOnly, setShowMyLocksOnly] = useState<boolean>(false);
  const [autoLockEnabled, setAutoLockEnabled] = useState<boolean>(true);
  const [predictiveLockingEnabled, setPredictiveLockingEnabled] = useState<boolean>(true);
  const [lockPriorityThreshold, setLockPriorityThreshold] = useState<LockPriority>(LockPriority.MEDIUM);
  
  // 当前用户ID
  const currentUserId = localStorage.getItem('userId') || '';
  
  // 初始化
  useEffect(() => {
    // 初始化锁定服务配置
    setAutoLockEnabled(smartLockingService.getAutoLockEnabled());
    setPredictiveLockingEnabled(smartLockingService.getPredictiveLockingEnabled());
    setLockPriorityThreshold(smartLockingService.getLockPriorityThreshold());
  }, []);
  
  // 处理锁定释放
  const handleReleaseLock = (lockId: string) => {
    if (smartLockingService.releaseLock(lockId)) {
      message.success(t('collaboration.lock.releaseSuccess'));
    } else {
      message.error(t('collaboration.lock.releaseFailed'));
    }
  };
  
  // 处理强制释放锁定
  const handleForciblyReleaseLock = (lockId: string) => {
    Modal.confirm({
      title: t('collaboration.lock.forciblyReleaseConfirm'),
      icon: <ExclamationCircleOutlined />,
      content: t('collaboration.lock.forciblyReleaseContent'),
      onOk() {
        if (smartLockingService.forciblyReleaseLock(lockId, t('collaboration.lock.forciblyReleaseReason'))) {
          message.success(t('collaboration.lock.forciblyReleaseSuccess'));
        } else {
          message.error(t('collaboration.lock.forciblyReleaseFailed'));
        }
      }
    });
  };
  
  // 处理设置变更
  const handleSettingsChange = () => {
    smartLockingService.setAutoLockEnabled(autoLockEnabled);
    smartLockingService.setPredictiveLockingEnabled(predictiveLockingEnabled);
    smartLockingService.setLockPriorityThreshold(lockPriorityThreshold);
    
    message.success(t('collaboration.lock.settingsSaved'));
    setShowSettings(false);
  };
  
  // 获取锁定类型显示文本
  const getLockTypeText = (type: LockType): string => {
    switch (type) {
      case LockType.ENTITY:
        return t('collaboration.lock.type.entity');
      case LockType.COMPONENT:
        return t('collaboration.lock.type.component');
      case LockType.PROPERTY:
        return t('collaboration.lock.type.property');
      case LockType.RESOURCE:
        return t('collaboration.lock.type.resource');
      case LockType.SCENE:
        return t('collaboration.lock.type.scene');
      case LockType.AREA:
        return t('collaboration.lock.type.area');
      default:
        return t('collaboration.lock.type.unknown');
    }
  };
  
  // 获取锁定状态显示文本和颜色
  const getLockStatusTag = (status: LockStatus): JSX.Element => {
    switch (status) {
      case LockStatus.ACTIVE:
        return <Tag color="green">{t('collaboration.lock.status.active')}</Tag>;
      case LockStatus.EXPIRED:
        return <Tag color="orange">{t('collaboration.lock.status.expired')}</Tag>;
      case LockStatus.RELEASED:
        return <Tag color="red">{t('collaboration.lock.status.released')}</Tag>;
      default:
        return <Tag>{t('collaboration.lock.status.unknown')}</Tag>;
    }
  };
  
  // 获取锁定优先级显示文本和颜色
  const getLockPriorityTag = (priority: LockPriority): JSX.Element => {
    switch (priority) {
      case LockPriority.LOW:
        return <Tag color="blue">{t('collaboration.lock.priority.low')}</Tag>;
      case LockPriority.MEDIUM:
        return <Tag color="green">{t('collaboration.lock.priority.medium')}</Tag>;
      case LockPriority.HIGH:
        return <Tag color="orange">{t('collaboration.lock.priority.high')}</Tag>;
      case LockPriority.CRITICAL:
        return <Tag color="red">{t('collaboration.lock.priority.critical')}</Tag>;
      default:
        return <Tag>{t('collaboration.lock.priority.unknown')}</Tag>;
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('collaboration.lock.user'),
      dataIndex: 'userName',
      key: 'userName',
      render: (text: string, record: Lock) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
          {record.userId === currentUserId && (
            <Tag color="purple">{t('collaboration.lock.you')}</Tag>
          )}
        </Space>
      )
    },
    {
      title: t('collaboration.lock.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: LockType) => getLockTypeText(type)
    },
    {
      title: t('collaboration.lock.target'),
      key: 'target',
      render: (_: string, record: Lock) => {
        if (record.type === LockType.ENTITY) {
          return <span>{record.entityId}</span>;
        } else if (record.type === LockType.COMPONENT) {
          return <span>{record.entityId}:{record.componentId}</span>;
        } else if (record.type === LockType.PROPERTY) {
          return <span>{record.entityId}:{record.componentId}:{record.propertyPath?.join('.')}</span>;
        } else if (record.type === LockType.RESOURCE) {
          return <span>{record.resourceId}</span>;
        } else if (record.type === LockType.AREA) {
          return <span>{record.areaId}</span>;
        } else {
          return <span>-</span>;
        }
      }
    },
    {
      title: t('collaboration.lock.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: LockStatus) => getLockStatusTag(status)
    },
    {
      title: t('collaboration.lock.priority'),
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: LockPriority) => getLockPriorityTag(priority)
    },
    {
      title: t('collaboration.lock.expires'),
      key: 'expires',
      render: (_: string, record: Lock) => {
        const now = Date.now();
        const timeLeft = Math.max(0, record.expiresAt - now);
        const seconds = Math.floor(timeLeft / 1000);
        
        return (
          <Space>
            <ClockCircleOutlined />
            <span>{seconds}s</span>
          </Space>
        );
      }
    },
    {
      title: t('collaboration.lock.actions'),
      key: 'actions',
      render: (_: string, record: Lock) => (
        <Space>
          {record.userId === currentUserId ? (
            <Button
              type="text"
              icon={<UnlockOutlined />}
              onClick={() => handleReleaseLock(record.id)}
              title={t('collaboration.lock.release') as string}
            />
          ) : (
            <Button
              type="text"
              danger
              icon={<UnlockOutlined />}
              onClick={() => handleForciblyReleaseLock(record.id)}
              title={t('collaboration.lock.forciblyRelease') as string}
            />
          )}
        </Space>
      )
    }
  ];
  
  // 过滤锁定列表
  const filteredLocks = Object.values(locks).filter(lock => {
    if (showMyLocksOnly) {
      return lock.userId === currentUserId;
    }
    return true;
  });
  
  // 渲染设置面板
  const renderSettingsPanel = () => (
    <Modal
      title={t('collaboration.lock.settings')}
      open={showSettings}
      onOk={handleSettingsChange}
      onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setShowSettings(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
    >
      <Form layout="vertical">
        <Form.Item
          label={t('collaboration.lock.autoLockEnabled')}
          tooltip={t('collaboration.lock.autoLockEnabledTooltip')}
        >
          <Switch
            checked={autoLockEnabled}
            onChange={setAutoLockEnabled}
          />
        </Form.Item>
        
        <Form.Item
          label={t('collaboration.lock.predictiveLockingEnabled')}
          tooltip={t('collaboration.lock.predictiveLockingEnabledTooltip')}
        >
          <Switch
            checked={predictiveLockingEnabled}
            onChange={setPredictiveLockingEnabled}
          />
        </Form.Item>
        
        <Form.Item
          label={t('collaboration.lock.lockPriorityThreshold')}
          tooltip={t('collaboration.lock.lockPriorityThresholdTooltip')}
        >
          <Select
            value={lockPriorityThreshold}
            onChange={setLockPriorityThreshold}
            style={{ width: '100%' }}
          >
            <Option value={LockPriority.LOW}>{t('collaboration.lock.priority.low')}</Option>
            <Option value={LockPriority.MEDIUM}>{t('collaboration.lock.priority.medium')}</Option>
            <Option value={LockPriority.HIGH}>{t('collaboration.lock.priority.high')}</Option>
            <Option value={LockPriority.CRITICAL}>{t('collaboration.lock.priority.critical')}</Option>
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  );
  
  return (
    <div className="lock-panel">
      <div className="lock-panel-header">
        <Title level={4}>{t('collaboration.lock.title')}</Title>
        <Space>
          <Switch
            checkedChildren={<EyeOutlined />}
            unCheckedChildren={<EyeInvisibleOutlined />}
            checked={!showMyLocksOnly}
            onChange={(checked) => setShowMyLocksOnly(!checked)}
            title={showMyLocksOnly ? t('collaboration.lock.showAll') as string : t('collaboration.lock.showMine') as string}
          />
          <Button
            icon={<SettingOutlined />}
            onClick={() => setShowSettings(true)}
            title={t('collaboration.lock.settings') as string}
          />
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {/* 刷新锁定列表 */}}
            title={t('collaboration.lock.refresh') as string}
          />
        </Space>
      </div>
      
      <div className="lock-panel-stats">
        <Card size="small">
          <Space>
            <Badge count={activeLocks.length} showZero color="green" />
            <Text>{t('collaboration.lock.activeLocks')}</Text>
          </Space>
        </Card>
        <Card size="small">
          <Space>
            <Badge count={myLocks.length} showZero color="blue" />
            <Text>{t('collaboration.lock.myLocks')}</Text>
          </Space>
        </Card>
      </div>
      
      <Divider style={{ margin: '12px 0' }} />
      
      {filteredLocks.length > 0 ? (
        <Table
          dataSource={filteredLocks}
          columns={columns}
          rowKey="id"
          size="small"
          pagination={{ pageSize: 10 }}
          onRow={(record) => ({
            onClick: () => setSelectedLockId(record.id),
            className: record.id === selectedLockId ? 'selected-row' : ''
          })}
        />
      ) : (
        <Empty description={t('collaboration.lock.noLocks')} />
      )}
      
      {renderSettingsPanel()}
    </div>
  );
};

export default LockPanel;
