/**
 * 高级角色控制器编辑器组件
 * 用于编辑高级角色控制器属性
 */
import React, { useState, useEffect } from 'react';
import { Form, InputNumber, Switch, Button, Tabs, Collapse, Tooltip, Space, Card, Row, Col, message } from 'antd';
import { DeleteOutlined, ImportOutlined, ExportOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateEntity } from '../../store/scene/sceneSlice';

import ControllerPresetSelector from './ControllerPresetSelector';
import ActionEditor from './ActionEditor';
import EnvironmentAwarenessEditor from './EnvironmentAwarenessEditor';
const { Panel } = Collapse;

interface AdvancedCharacterControllerEditorProps {
  entityId: string;
}

/**
 * 高级角色控制器编辑器组件
 */
const AdvancedCharacterControllerEditor: React.FC<AdvancedCharacterControllerEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 获取实体数据
  const entity = useSelector((state: RootState) =>
    state.scene.entities.find((entity: any) => entity.id === entityId)
  );
  
  // 获取角色控制器组件数据
  const controllerComponent = entity?.components?.advancedCharacterController;
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 当前选中的预设
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  
  // 是否显示预设选择器
  const [showPresetSelector, setShowPresetSelector] = useState(false);
  
  // 是否显示动作编辑器
  const [showActionEditor, setShowActionEditor] = useState(false);
  
  // 是否显示环境感知编辑器
  const [showEnvironmentEditor, setShowEnvironmentEditor] = useState(false);
  
  // 初始化表单
  useEffect(() => {
    if (controllerComponent) {
      form.setFieldsValue({
        // 基本设置
        walkSpeed: controllerComponent.walkSpeed || 2.0,
        runSpeed: controllerComponent.runSpeed || 5.0,
        crouchSpeed: controllerComponent.crouchSpeed || 1.0,
        crawlSpeed: controllerComponent.crawlSpeed || 0.5,
        swimSpeed: controllerComponent.swimSpeed || 1.5,
        climbSpeed: controllerComponent.climbSpeed || 1.0,
        flySpeed: controllerComponent.flySpeed || 8.0,
        jumpForce: controllerComponent.jumpForce || 5.0,
        gravity: controllerComponent.gravity || 9.8,
        turnSpeed: controllerComponent.turnSpeed || 2.0,
        airControl: controllerComponent.airControl || 0.3,
        
        // 功能开关
        usePhysics: controllerComponent.usePhysics !== false,
        useStateMachine: controllerComponent.useStateMachine !== false,
        useBlendSpace: controllerComponent.useBlendSpace !== false,
        useIK: controllerComponent.useIK !== false,
        useEnvironmentAwareness: controllerComponent.useEnvironmentAwareness !== false,
        debug: controllerComponent.debug || false,
        
        // 物理控制器设置
        physicsOffset: controllerComponent.physicsControllerOptions?.offset || 0.01,
        maxSlopeClimbAngle: controllerComponent.physicsControllerOptions?.maxSlopeClimbAngle 
          ? (controllerComponent.physicsControllerOptions.maxSlopeClimbAngle * 180 / Math.PI) 
          : 45,
        minSlopeSlideAngle: controllerComponent.physicsControllerOptions?.minSlopeSlideAngle 
          ? (controllerComponent.physicsControllerOptions.minSlopeSlideAngle * 180 / Math.PI) 
          : 30,
        enableAutoStep: controllerComponent.physicsControllerOptions?.autoStep ? true : false,
        autoStepMaxHeight: controllerComponent.physicsControllerOptions?.autoStep?.maxHeight || 0.5,
        autoStepMinWidth: controllerComponent.physicsControllerOptions?.autoStep?.minWidth || 0.1,
        autoStepOverDynamic: controllerComponent.physicsControllerOptions?.autoStep?.stepOverDynamic || true,
        enableSnapToGround: controllerComponent.physicsControllerOptions?.enableSnapToGround ? true : false,
        snapToGroundDistance: typeof controllerComponent.physicsControllerOptions?.enableSnapToGround === 'number' 
          ? controllerComponent.physicsControllerOptions.enableSnapToGround 
          : 0.1
      });
    }
  }, [controllerComponent, form]);
  
  // 处理表单值变更
  const handleValuesChange = (_changedValues: any, allValues: any) => {
    if (!entity) return;
    
    // 创建物理控制器选项
    const physicsControllerOptions = {
      offset: allValues.physicsOffset,
      maxSlopeClimbAngle: (allValues.maxSlopeClimbAngle * Math.PI) / 180, // 转换为弧度
      minSlopeSlideAngle: (allValues.minSlopeSlideAngle * Math.PI) / 180, // 转换为弧度
      autoStep: allValues.enableAutoStep ? {
        maxHeight: allValues.autoStepMaxHeight,
        minWidth: allValues.autoStepMinWidth,
        stepOverDynamic: allValues.autoStepOverDynamic
      } : undefined,
      enableSnapToGround: allValues.enableSnapToGround ? allValues.snapToGroundDistance : false
    };
    
    // 创建控制器选项
    const options = {
      walkSpeed: allValues.walkSpeed,
      runSpeed: allValues.runSpeed,
      crouchSpeed: allValues.crouchSpeed,
      crawlSpeed: allValues.crawlSpeed,
      swimSpeed: allValues.swimSpeed,
      climbSpeed: allValues.climbSpeed,
      flySpeed: allValues.flySpeed,
      jumpForce: allValues.jumpForce,
      gravity: allValues.gravity,
      turnSpeed: allValues.turnSpeed,
      airControl: allValues.airControl,
      usePhysics: allValues.usePhysics,
      useStateMachine: allValues.useStateMachine,
      useBlendSpace: allValues.useBlendSpace,
      useIK: allValues.useIK,
      useEnvironmentAwareness: allValues.useEnvironmentAwareness,
      debug: allValues.debug,
      physicsControllerOptions
    };
    
    // 更新实体的角色控制器组件
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: {
          ...entity.components,
          advancedCharacterController: options
        }
      }
    }));
  };
  
  // 处理移除组件
  const handleRemoveComponent = () => {
    if (!entity) return;
    
    // 创建新的组件对象，不包含角色控制器组件
    const { advancedCharacterController, ...otherComponents } = entity.components || {};
    
    // 更新实体
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: otherComponents
      }
    }));
  };
  
  // 处理应用预设
  const handleApplyPreset = (presetId: string) => {
    // 这里应该调用预设管理器获取预设数据并应用
    // 示例代码，实际实现需要与预设管理器集成
    message.success(t('editor.avatar.presetApplied', { name: presetId }));
    setSelectedPreset(presetId);
    setShowPresetSelector(false);
  };
  
  // 处理导出配置
  const handleExportConfig = () => {
    if (!controllerComponent) return;
    
    // 创建配置对象
    const config = {
      ...controllerComponent,
      exportTime: new Date().toISOString()
    };
    
    // 转换为JSON
    const json = JSON.stringify(config, null, 2);
    
    // 创建下载链接
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `character_controller_${entityId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success(t('editor.common.exportSuccess'));
  };
  
  // 处理导入配置
  const handleImportConfig = () => {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const config = JSON.parse(event.target?.result as string);
          
          // 更新表单
          form.setFieldsValue({
            // 基本设置
            walkSpeed: config.walkSpeed || 2.0,
            runSpeed: config.runSpeed || 5.0,
            crouchSpeed: config.crouchSpeed || 1.0,
            crawlSpeed: config.crawlSpeed || 0.5,
            swimSpeed: config.swimSpeed || 1.5,
            climbSpeed: config.climbSpeed || 1.0,
            flySpeed: config.flySpeed || 8.0,
            jumpForce: config.jumpForce || 5.0,
            gravity: config.gravity || 9.8,
            turnSpeed: config.turnSpeed || 2.0,
            airControl: config.airControl || 0.3,
            
            // 功能开关
            usePhysics: config.usePhysics !== false,
            useStateMachine: config.useStateMachine !== false,
            useBlendSpace: config.useBlendSpace !== false,
            useIK: config.useIK !== false,
            useEnvironmentAwareness: config.useEnvironmentAwareness !== false,
            debug: config.debug || false,
            
            // 物理控制器设置
            physicsOffset: config.physicsControllerOptions?.offset || 0.01,
            maxSlopeClimbAngle: config.physicsControllerOptions?.maxSlopeClimbAngle 
              ? (config.physicsControllerOptions.maxSlopeClimbAngle * 180 / Math.PI) 
              : 45,
            minSlopeSlideAngle: config.physicsControllerOptions?.minSlopeSlideAngle 
              ? (config.physicsControllerOptions.minSlopeSlideAngle * 180 / Math.PI) 
              : 30,
            enableAutoStep: config.physicsControllerOptions?.autoStep ? true : false,
            autoStepMaxHeight: config.physicsControllerOptions?.autoStep?.maxHeight || 0.5,
            autoStepMinWidth: config.physicsControllerOptions?.autoStep?.minWidth || 0.1,
            autoStepOverDynamic: config.physicsControllerOptions?.autoStep?.stepOverDynamic || true,
            enableSnapToGround: config.physicsControllerOptions?.enableSnapToGround ? true : false,
            snapToGroundDistance: typeof config.physicsControllerOptions?.enableSnapToGround === 'number' 
              ? config.physicsControllerOptions.enableSnapToGround 
              : 0.1
          });
          
          // 触发表单变更
          handleValuesChange({}, form.getFieldsValue());
          
          message.success(t('editor.common.importSuccess'));
        } catch (error) {
          message.error(t('editor.common.importError'));
          console.error('导入配置失败:', error);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };
  
  // 如果没有实体或角色控制器组件，显示空状态
  if (!entity) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.common.noEntitySelected')}</p>
      </div>
    );
  }
  
  // 如果没有角色控制器组件，显示添加按钮
  if (!controllerComponent) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.avatar.noAdvancedCharacterController')}</p>
        <Button
          type="primary"
          onClick={() => {
            if (entity) {
              dispatch(updateEntity({
                id: entityId,
                changes: {
                  components: {
                    ...entity.components,
                    advancedCharacterController: {
                      walkSpeed: 2.0,
                      runSpeed: 5.0,
                      jumpForce: 5.0,
                      gravity: 9.8,
                      usePhysics: true,
                      useStateMachine: true,
                      useBlendSpace: true,
                      physicsControllerOptions: {
                        offset: 0.01,
                        maxSlopeClimbAngle: (45 * Math.PI) / 180,
                        minSlopeSlideAngle: (30 * Math.PI) / 180,
                        autoStep: {
                          maxHeight: 0.5,
                          minWidth: 0.1,
                          stepOverDynamic: true
                        },
                        enableSnapToGround: 0.1
                      }
                    }
                  }
                }
              }));
            }
          }}
        >
          {t('editor.avatar.addAdvancedCharacterController')}
        </Button>
      </div>
    );
  }

  // 标签页配置
  const tabItems = [
    {
      key: 'basic',
      label: t('editor.avatar.basicSettings'),
      children: (
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
        >
          <Card title={t('editor.avatar.movementSettings')} size="small" className="settings-card">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="walkSpeed"
                  label={t('editor.avatar.walkSpeed')}
                  tooltip={t('editor.avatar.walkSpeedTooltip')}
                  rules={[{ type: 'number', min: 0.1 }]}
                >
                  <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="runSpeed"
                  label={t('editor.avatar.runSpeed')}
                  tooltip={t('editor.avatar.runSpeedTooltip')}
                  rules={[{ type: 'number', min: 0.1 }]}
                >
                  <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="crouchSpeed"
                  label={t('editor.avatar.crouchSpeed')}
                  tooltip={t('editor.avatar.crouchSpeedTooltip')}
                  rules={[{ type: 'number', min: 0.1 }]}
                >
                  <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="jumpForce"
                  label={t('editor.avatar.jumpForce')}
                  tooltip={t('editor.avatar.jumpForceTooltip')}
                  rules={[{ type: 'number', min: 0.1 }]}
                >
                  <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="gravity"
                  label={t('editor.avatar.gravity')}
                  tooltip={t('editor.avatar.gravityTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="turnSpeed"
                  label={t('editor.avatar.turnSpeed')}
                  tooltip={t('editor.avatar.turnSpeedTooltip')}
                  rules={[{ type: 'number', min: 0.1 }]}
                >
                  <InputNumber min={0.1} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="airControl"
              label={t('editor.avatar.airControl')}
              tooltip={t('editor.avatar.airControlTooltip')}
              rules={[{ type: 'number', min: 0, max: 1 }]}
            >
              <InputNumber min={0} max={1} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
          </Card>

          <Card title={t('editor.avatar.featureSettings')} size="small" className="settings-card">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="usePhysics"
                  label={t('editor.avatar.usePhysics')}
                  tooltip={t('editor.avatar.usePhysicsTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="useStateMachine"
                  label={t('editor.avatar.useStateMachine')}
                  tooltip={t('editor.avatar.useStateMachineTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="useBlendSpace"
                  label={t('editor.avatar.useBlendSpace')}
                  tooltip={t('editor.avatar.useBlendSpaceTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="useIK"
                  label={t('editor.avatar.useIK')}
                  tooltip={t('editor.avatar.useIKTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="useEnvironmentAwareness"
                  label={t('editor.avatar.useEnvironmentAwareness')}
                  tooltip={t('editor.avatar.useEnvironmentAwarenessTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="debug"
                  label={t('editor.common.debug')}
                  tooltip={t('editor.common.debugTooltip')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      )
    },
    {
      key: 'physics',
      label: t('editor.avatar.physicsSettings'),
      children: (
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
        >
          <Form.Item
            name="physicsOffset"
            label={t('editor.physics.offset')}
            tooltip={t('editor.physics.characterOffsetTooltip')}
            rules={[{ type: 'number', min: 0.001 }]}
          >
            <InputNumber min={0.001} step={0.01} style={{ width: '100%' }} />
          </Form.Item>

          <Collapse defaultActiveKey={['slopes', 'autoStep', 'snapToGround']}>
            <Panel header={t('editor.physics.slopeSettings')} key="slopes">
              <Form.Item
                name="maxSlopeClimbAngle"
                label={t('editor.physics.maxSlopeClimbAngle')}
                tooltip={t('editor.physics.maxSlopeClimbAngleTooltip')}
                rules={[{ type: 'number', min: 0, max: 90 }]}
              >
                <InputNumber min={0} max={90} step={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="minSlopeSlideAngle"
                label={t('editor.physics.minSlopeSlideAngle')}
                tooltip={t('editor.physics.minSlopeSlideAngleTooltip')}
                rules={[{ type: 'number', min: 0, max: 90 }]}
              >
                <InputNumber min={0} max={90} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Panel>

            <Panel header={t('editor.physics.autoStepSettings')} key="autoStep">
              <Form.Item
                name="enableAutoStep"
                label={t('editor.physics.enableAutoStep')}
                tooltip={t('editor.physics.enableAutoStepTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="autoStepMaxHeight"
                label={t('editor.physics.autoStepMaxHeight')}
                tooltip={t('editor.physics.autoStepMaxHeightTooltip')}
                rules={[{ type: 'number', min: 0.01 }]}
              >
                <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="autoStepMinWidth"
                label={t('editor.physics.autoStepMinWidth')}
                tooltip={t('editor.physics.autoStepMinWidthTooltip')}
                rules={[{ type: 'number', min: 0.01 }]}
              >
                <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="autoStepOverDynamic"
                label={t('editor.physics.autoStepOverDynamic')}
                tooltip={t('editor.physics.autoStepOverDynamicTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Panel>

            <Panel header={t('editor.physics.snapToGroundSettings')} key="snapToGround">
              <Form.Item
                name="enableSnapToGround"
                label={t('editor.physics.enableSnapToGround')}
                tooltip={t('editor.physics.enableSnapToGroundTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="snapToGroundDistance"
                label={t('editor.physics.snapToGroundDistance')}
                tooltip={t('editor.physics.snapToGroundDistanceTooltip')}
                rules={[{ type: 'number', min: 0.01 }]}
              >
                <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
              </Form.Item>
            </Panel>
          </Collapse>
        </Form>
      )
    },
    {
      key: 'presets',
      label: t('editor.avatar.presets'),
      children: (
        <div className="presets-container">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowPresetSelector(true)}
            style={{ marginBottom: 16 }}
          >
            {t('editor.avatar.applyPreset')}
          </Button>

          {/* 这里应该显示已应用的预设信息 */}
          {selectedPreset && (
            <Card title={t('editor.avatar.appliedPreset')} size="small">
              <p>{selectedPreset}</p>
            </Card>
          )}

          {/* 预设选择器对话框 */}
          {showPresetSelector && (
            <ControllerPresetSelector
              visible={showPresetSelector}
              onClose={() => {
          try {
            setShowPresetSelector(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
              onSelect={handleApplyPreset}
            />
          )}
        </div>
      )
    },
    {
      key: 'actions',
      label: t('editor.avatar.actions'),
      children: (
        <div className="actions-container">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowActionEditor(true)}
            style={{ marginBottom: 16 }}
          >
            {t('editor.avatar.editActions')}
          </Button>

          {/* 动作编辑器对话框 */}
          {showActionEditor && (
            <ActionEditor
              visible={showActionEditor}
              entityId={entityId}
              onClose={() => {
          try {
            setShowActionEditor(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
            />
          )}
        </div>
      )
    },
    {
      key: 'environment',
      label: t('editor.avatar.environmentAwareness'),
      children: (
        <div className="environment-container">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowEnvironmentEditor(true)}
            style={{ marginBottom: 16 }}
          >
            {t('editor.avatar.editEnvironmentAwareness')}
          </Button>

          {/* 环境感知编辑器对话框 */}
          {showEnvironmentEditor && (
            <EnvironmentAwarenessEditor
              visible={showEnvironmentEditor}
              entityId={entityId}
              onClose={() => {
          try {
            setShowEnvironmentEditor(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
            />
          )}
        </div>
      )
    }
  ];

  return (
    <div className="component-editor advanced-character-controller-editor">
      <div className="component-header">
        <h3>{t('editor.avatar.advancedCharacterController')}</h3>
        <Space>
          <Tooltip title={t('editor.common.export')}>
            <Button 
              type="text" 
              icon={<ExportOutlined />} 
              onClick={handleExportConfig}
            />
          </Tooltip>
          <Tooltip title={t('editor.common.import')}>
            <Button 
              type="text" 
              icon={<ImportOutlined />} 
              onClick={handleImportConfig}
            />
          </Tooltip>
          <Tooltip title={t('editor.common.remove')}>
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={handleRemoveComponent}
            />
          </Tooltip>
        </Space>
      </div>
      
      <Tabs
        defaultActiveKey="basic"
        items={tabItems}
      />
    </div>
  );
};

export default AdvancedCharacterControllerEditor;
