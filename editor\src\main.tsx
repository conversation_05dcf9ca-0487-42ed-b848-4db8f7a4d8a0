/**
 * 编辑器入口文件
 */
import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { I18nextProvider } from 'react-i18next';
import zhCN from 'antd/locale/zh_CN';
import i18n from './i18n';

import App from './App';
import { store } from './store';
import './styles/index.less';

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('应用错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          backgroundColor: '#f5f5f5',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <h1>应用加载失败</h1>
          <p>请刷新页面重试，或联系技术支持。</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            刷新页面
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 确保DOM元素存在
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// 创建React根节点并渲染应用
const root = ReactDOM.createRoot(rootElement);

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  // 防止错误导致应用完全崩溃
  event.preventDefault();
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
  // 防止未处理的Promise拒绝导致应用崩溃
  event.preventDefault();
});

// 等待i18n初始化完成后再渲染应用
const renderApp = () => {
  try {
    root.render(
      <ErrorBoundary>
        <I18nextProvider i18n={i18n}>
          <Provider store={store}>
            <BrowserRouter>
              <ConfigProvider locale={zhCN}>
                <App />
              </ConfigProvider>
            </BrowserRouter>
          </Provider>
        </I18nextProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error('应用渲染失败:', error);
    // 显示基本的错误页面
    root.render(
      <div style={{
        padding: '20px',
        textAlign: 'center',
        backgroundColor: '#f5f5f5',
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h1>应用加载失败</h1>
        <p>请刷新页面重试，或联系技术支持。</p>
        <button
          onClick={() => {
            try {
              window.location.reload();
            } catch (reloadError) {
              console.error('刷新页面失败:', reloadError);
            }
          }}
          style={{
            padding: '10px 20px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          刷新页面
        </button>
      </div>
    );
  }
};

// 如果i18n已经初始化，直接渲染
try {
  if (i18n.isInitialized) {
    renderApp();
  } else {
    // 等待i18n初始化完成
    i18n.on('initialized', renderApp);
  }
} catch (error) {
  console.error('i18n初始化失败:', error);
  renderApp(); // 尝试直接渲染
}
