/**
 * 交互编辑器
 * 用于编辑和配置交互系统相关的组件和行为
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Button,
  Space,
  Divider,
  Alert,
  Collapse,
  Tag,
  Tooltip,
  Row,
  Col,
  ColorPicker,
  Slider,
  Typography,
  List,
  Modal,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  ExperimentOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { updateEntity } from '../../store/scene/sceneSlice';
import './InteractionEditor.less';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Text, Title } = Typography;
const { Option } = Select;

// 交互类型枚举
export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  PROXIMITY = 'proximity',
  GRAB = 'grab',
  TOUCH = 'touch'
}

// 交互事件类型
export enum InteractionEventType {
  START = 'start',
  UPDATE = 'update',
  END = 'end',
  CANCEL = 'cancel'
}

// 高亮类型
export enum HighlightType {
  OUTLINE = 'outline',
  GLOW = 'glow',
  COLOR = 'color',
  SCALE = 'scale'
}

// 提示位置类型
export enum PromptPositionType {
  WORLD = 'world',
  SCREEN = 'screen',
  ATTACHED = 'attached'
}

// 可交互组件配置接口
interface InteractableComponentConfig {
  interactionType: InteractionType;
  visible: boolean;
  interactive: boolean;
  interactionDistance: number;
  label: string;
  prompt: string;
  interactionSound?: string;
  highlightColor: string;
  highlightType: HighlightType;
  enableHighlight: boolean;
  enablePrompt: boolean;
  enableSound: boolean;
  priority: number;
  conditions: InteractionCondition[];
  events: InteractionEventConfig[];
}

// 交互条件接口
interface InteractionCondition {
  id: string;
  type: 'distance' | 'angle' | 'state' | 'inventory' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater' | 'less' | 'contains';
  value: any;
  enabled: boolean;
}

// 交互事件配置接口
interface InteractionEventConfig {
  id: string;
  eventType: InteractionEventType;
  actions: InteractionAction[];
  enabled: boolean;
}

// 交互动作接口
interface InteractionAction {
  id: string;
  type: 'animation' | 'sound' | 'script' | 'ui' | 'physics' | 'teleport';
  parameters: Record<string, any>;
  delay: number;
  enabled: boolean;
}

/**
 * 交互编辑器属性
 */
interface InteractionEditorProps {
  /** 组件数据 */
  data?: InteractableComponentConfig;
  /** 数据更新回调 */
  onChange?: (data: InteractableComponentConfig) => void;
  /** 是否只读模式 */
  readonly?: boolean;
}

/**
 * 交互编辑器组件
 */
const InteractionEditor: React.FC<InteractionEditorProps> = ({
  data,
  onChange,
  readonly = false
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const selectedEntityId = useAppSelector((state) => state.scene.selectedEntityId);
  const entities = useAppSelector((state) => state.scene.entities);
  const selectedEntity = selectedEntityId ? entities.find(entity => entity.id === selectedEntityId) : null;

  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isPreviewMode, setIsPreviewMode] = useState<boolean>(false);
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [conditionModalVisible, setConditionModalVisible] = useState<boolean>(false);
  const [eventModalVisible, setEventModalVisible] = useState<boolean>(false);
  const [editingCondition, setEditingCondition] = useState<InteractionCondition | null>(null);
  const [editingEvent, setEditingEvent] = useState<InteractionEventConfig | null>(null);

  // 默认配置
  const defaultConfig: InteractableComponentConfig = {
    interactionType: InteractionType.CLICK,
    visible: true,
    interactive: true,
    interactionDistance: 5.0,
    label: t('interaction.defaultLabel'),
    prompt: t('interaction.defaultPrompt'),
    highlightColor: '#ffff00',
    highlightType: HighlightType.OUTLINE,
    enableHighlight: true,
    enablePrompt: true,
    enableSound: false,
    priority: 0,
    conditions: [],
    events: []
  };

  // 当前配置状态
  const [config, setConfig] = useState<InteractableComponentConfig>(
    data || defaultConfig
  );

  // 初始化表单数据
  useEffect(() => {
    if (data) {
      setConfig(data);
      form.setFieldsValue(data);
    }
  }, [data, form]);

  // 处理配置更新
  const handleConfigChange = useCallback((changedFields: Partial<InteractableComponentConfig>) => {
    const newConfig = { ...config, ...changedFields };
    setConfig(newConfig);

    if (onChange && !readonly) {
      onChange(newConfig);
    }

    // 如果有选中的实体，更新实体组件
    if (selectedEntity) {
      dispatch(updateEntity({
        id: selectedEntity.id,
        changes: {
          components: {
            ...selectedEntity.components,
            Interactable: newConfig
          }
        }
      }));
    }
  }, [config, onChange, readonly, selectedEntity, dispatch]);

  return (
    <Card
      className="interaction-editor"
      title={
        <Space>
          <ExperimentOutlined />
          {t('interaction.title')}
          {isPreviewMode && <Tag color="green">{t('interaction.previewMode')}</Tag>}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={t('interaction.togglePreview')}>
            <Button
              type={isPreviewMode ? 'primary' : 'default'}
              icon={isPreviewMode ? <StopOutlined /> : <PlayCircleOutlined />}
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              disabled={readonly}
            />
          </Tooltip>
          <Tooltip title={t('interaction.advancedSettings')}>
            <Button
              type={showAdvanced ? 'primary' : 'default'}
              icon={<SettingOutlined />}
              onClick={() => setShowAdvanced(!showAdvanced)}
            />
          </Tooltip>
        </Space>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <InfoCircleOutlined />
              {t('interaction.basicSettings')}
            </span>
          }
          key="basic"
        >
          <Form
            form={form}
            layout="vertical"
            onValuesChange={(changedValues) => handleConfigChange(changedValues)}
            disabled={readonly}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="interactionType"
                  label={t('interaction.interactionType')}
                  tooltip={t('interaction.interactionTypeTooltip')}
                >
                  <Select>
                    <Option value={InteractionType.CLICK}>{t('interaction.click')}</Option>
                    <Option value={InteractionType.HOVER}>{t('interaction.hover')}</Option>
                    <Option value={InteractionType.PROXIMITY}>{t('interaction.proximity')}</Option>
                    <Option value={InteractionType.GRAB}>{t('interaction.grab')}</Option>
                    <Option value={InteractionType.TOUCH}>{t('interaction.touch')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="interactionDistance"
                  label={t('interaction.interactionDistance')}
                  tooltip={t('interaction.interactionDistanceTooltip')}
                >
                  <InputNumber
                    min={0.1}
                    max={100}
                    step={0.1}
                    addonAfter="m"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="label"
                  label={t('interaction.label')}
                  tooltip={t('interaction.labelTooltip')}
                >
                  <Input placeholder={t('interaction.labelPlaceholder') as string} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="prompt"
                  label={t('interaction.prompt')}
                  tooltip={t('interaction.promptTooltip')}
                >
                  <Input placeholder={t('interaction.promptPlaceholder') as string} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="visible"
                  label={t('interaction.visible')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="interactive"
                  label={t('interaction.interactive')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="priority"
                  label={t('interaction.priority')}
                  tooltip={t('interaction.priorityTooltip')}
                >
                  <InputNumber
                    min={-10}
                    max={10}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Divider orientation="left">{t('interaction.visualSettings')}</Divider>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="enableHighlight"
                  label={t('interaction.enableHighlight')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="highlightType"
                  label={t('interaction.highlightType')}
                >
                  <Select disabled={!config.enableHighlight}>
                    <Option value={HighlightType.OUTLINE}>{t('interaction.outline')}</Option>
                    <Option value={HighlightType.GLOW}>{t('interaction.glow')}</Option>
                    <Option value={HighlightType.COLOR}>{t('interaction.color')}</Option>
                    <Option value={HighlightType.SCALE}>{t('interaction.scale')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="highlightColor"
                  label={t('interaction.highlightColor')}
                >
                  <ColorPicker
                    disabled={!config.enableHighlight}
                    showText
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="enablePrompt"
                  label={t('interaction.enablePrompt')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="enableSound"
                  label={t('interaction.enableSound')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            {config.enableSound && (
              <Form.Item
                name="interactionSound"
                label={t('interaction.interactionSound')}
                tooltip={t('interaction.interactionSoundTooltip')}
              >
                <Input placeholder={t('interaction.soundPathPlaceholder') as string} />
              </Form.Item>
            )}
          </Form>
        </TabPane>

        <TabPane
          tab={
            <span>
              <BugOutlined />
              {t('interaction.conditions')}
            </span>
          }
          key="conditions"
        >
          <div className="interaction-conditions">
            <div className="conditions-header">
              <Title level={5}>{t('interaction.interactionConditions')}</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setConditionModalVisible(true)}
                disabled={readonly}
              >
                {t('interaction.addCondition')}
              </Button>
            </div>

            <List
              dataSource={config.conditions}
              renderItem={(condition, index) => (
                <List.Item
                  actions={[
                    <Button
                      key="edit"
                      type="link"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setEditingCondition(condition);
                        setConditionModalVisible(true);
                      }}
                      disabled={readonly}
                    />,
                    <Button
                      key="delete"
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const newConditions = config.conditions.filter((_, i) => i !== index);
                        handleConfigChange({ conditions: newConditions });
                      }}
                      disabled={readonly}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Tag color={condition.enabled ? 'green' : 'red'}>
                          {condition.enabled ? t('common.enabled') : t('common.disabled')}
                        </Tag>
                        <Text strong>{condition.type}</Text>
                      </Space>
                    }
                    description={`${condition.operator}: ${condition.value}`}
                  />
                </List.Item>
              )}
              locale={{ emptyText: t('interaction.noConditions') }}
            />
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <PlayCircleOutlined />
              {t('interaction.events')}
            </span>
          }
          key="events"
        >
          <div className="interaction-events">
            <div className="events-header">
              <Title level={5}>{t('interaction.interactionEvents')}</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setEventModalVisible(true)}
                disabled={readonly}
              >
                {t('interaction.addEvent')}
              </Button>
            </div>

            <List
              dataSource={config.events}
              renderItem={(event, index) => (
                <List.Item
                  actions={[
                    <Button
                      key="edit"
                      type="link"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setEditingEvent(event);
                        setEventModalVisible(true);
                      }}
                      disabled={readonly}
                    />,
                    <Button
                      key="delete"
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const newEvents = config.events.filter((_, i) => i !== index);
                        handleConfigChange({ events: newEvents });
                      }}
                      disabled={readonly}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Tag color={event.enabled ? 'green' : 'red'}>
                          {event.enabled ? t('common.enabled') : t('common.disabled')}
                        </Tag>
                        <Text strong>{event.eventType}</Text>
                      </Space>
                    }
                    description={t('interaction.actionsCount', { count: event.actions.length })}
                  />
                </List.Item>
              )}
              locale={{ emptyText: t('interaction.noEvents') }}
            />
          </div>
        </TabPane>

        {showAdvanced && (
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('interaction.advanced')}
              </span>
            }
            key="advanced"
          >
            <Alert
              message={t('interaction.advancedWarning')}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Collapse>
              <Panel header={t('interaction.systemSettings')} key="system">
                <Form layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label={t('interaction.updateFrequency')}>
                        <Slider
                          min={1}
                          max={60}
                          marks={{
                            1: '1Hz',
                            10: '10Hz',
                            30: '30Hz',
                            60: '60Hz'
                          }}
                          tooltip={{ formatter: (value) => `${value}Hz` }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label={t('interaction.maxInteractables')}>
                        <InputNumber
                          min={1}
                          max={1000}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </Panel>

              <Panel header={t('interaction.debugSettings')} key="debug">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Switch
                    checkedChildren={t('interaction.debugEnabled')}
                    unCheckedChildren={t('interaction.debugDisabled')}
                  />
                  <Switch
                    checkedChildren={t('interaction.showBounds')}
                    unCheckedChildren={t('interaction.hideBounds')}
                  />
                  <Switch
                    checkedChildren={t('interaction.showRays')}
                    unCheckedChildren={t('interaction.hideRays')}
                  />
                </Space>
              </Panel>
            </Collapse>
          </TabPane>
        )}
      </Tabs>

      {/* 条件编辑模态框 */}
      <Modal
        title={editingCondition ? t('interaction.editCondition') : t('interaction.addCondition')}
        open={conditionModalVisible}
        onOk={() = destroyOnClose keyboard={true} maskClosable={true}> {
          // 处理条件保存逻辑
          setConditionModalVisible(false);
          setEditingCondition(null);
          message.success(t('interaction.conditionSaved'));
        }}
        onCancel={() => {
          try {
            {
          setConditionModalVisible(false);
          setEditingCondition(null);
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}}
        width={600}
      >
        {/* 条件编辑表单内容 */}
        <Form layout="vertical">
          <Form.Item label={t('interaction.conditionType')}>
            <Select placeholder={t('interaction.selectConditionType') as string}>
              <Option value="distance">{t('interaction.distance')}</Option>
              <Option value="angle">{t('interaction.angle')}</Option>
              <Option value="state">{t('interaction.state')}</Option>
              <Option value="inventory">{t('interaction.inventory')}</Option>
              <Option value="custom">{t('interaction.custom')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 事件编辑模态框 */}
      <Modal
        title={editingEvent ? t('interaction.editEvent') : t('interaction.addEvent')}
        open={eventModalVisible}
        onOk={() = destroyOnClose keyboard={true} maskClosable={true}> {
          // 处理事件保存逻辑
          setEventModalVisible(false);
          setEditingEvent(null);
          message.success(t('interaction.eventSaved'));
        }}
        onCancel={() => {
          try {
            {
          setEventModalVisible(false);
          setEditingEvent(null);
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}}
        width={800}
      >
        {/* 事件编辑表单内容 */}
        <Form layout="vertical">
          <Form.Item label={t('interaction.eventType')}>
            <Select placeholder={t('interaction.selectEventType') as string}>
              <Option value={InteractionEventType.START}>{t('interaction.start')}</Option>
              <Option value={InteractionEventType.UPDATE}>{t('interaction.update')}</Option>
              <Option value={InteractionEventType.END}>{t('interaction.end')}</Option>
              <Option value={InteractionEventType.CANCEL}>{t('interaction.cancel')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default InteractionEditor;
