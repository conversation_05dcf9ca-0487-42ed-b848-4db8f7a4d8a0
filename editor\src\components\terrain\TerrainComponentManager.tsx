/**
 * 地形组件管理器
 * 负责地形组件的创建、编辑和管理
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Modal, Form, InputNumber, Switch, Select, message, Tabs, Divider, Upload } from 'antd';
import { PlusOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { selectTerrain, importTerrain } from '../../store/terrain/terrainSlice';
import { terrainEngineService } from '../../services/TerrainEngineService';
import TerrainGenerationTool from './TerrainGenerationTool';
import TerrainSculptingTool from './TerrainSculptingTool';
import TerrainPreview from './TerrainPreview';
import './TerrainComponentManager.less';

const { Option } = Select;

interface TerrainComponentManagerProps {
  entityId?: string;
  onTerrainCreated?: (entityId: string) => void;
  onTerrainDeleted?: (entityId: string) => void;
}

/**
 * 地形组件管理器
 */
export const TerrainComponentManager: React.FC<TerrainComponentManagerProps> = ({
  entityId,
  onTerrainCreated,
  onTerrainDeleted
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  // Redux状态
  const selectedTerrainId = useAppSelector(state => state.terrain.selectedTerrainId);
  const terrainComponents = useAppSelector(state => state.terrain.terrainComponents);
  const isGenerating = useAppSelector(state => state.terrain.isGenerating);
  const isImporting = useAppSelector(state => state.terrain.isImporting);
  const error = useAppSelector(state => state.terrain.error);
  
  // 本地状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('generation');
  const [form] = Form.useForm();
  const [importForm] = Form.useForm();

  // 当前选中的地形组件
  const currentTerrain = selectedTerrainId ? terrainComponents[selectedTerrainId] : null;

  useEffect(() => {
    if (entityId && entityId !== selectedTerrainId) {
      dispatch(selectTerrain(entityId));
    }
  }, [entityId, selectedTerrainId, dispatch]);

  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);

  /**
   * 创建地形组件
   */
  const handleCreateTerrain = async (values: any) => {
    try {
      const newEntityId = `terrain_${Date.now()}`;
      
      await terrainEngineService.createTerrainComponent(newEntityId, {
        width: values.width,
        height: values.height,
        resolution: values.resolution,
        maxHeight: values.maxHeight,
        useLOD: values.useLOD,
        usePhysics: values.usePhysics
      });

      dispatch(selectTerrain(newEntityId));
      setCreateModalVisible(false);
      form.resetFields();
      
      if (onTerrainCreated) {
        onTerrainCreated(newEntityId);
      }
      
      message.success(t('terrain.createSuccess'));
    } catch (error: any) {
      message.error(t('terrain.createFailed') + ': ' + error.message);
    }
  };

  /**
   * 导入地形
   */
  const handleImportTerrain = async (values: any) => {
    try {
      if (!values.file || !values.file.file) {
        message.error(t('terrain.selectFile'));
        return;
      }

      const targetEntityId = selectedTerrainId || `terrain_${Date.now()}`;
      
      await dispatch(importTerrain({
        entityId: targetEntityId,
        file: values.file.file,
        options: {
          applySmoothing: values.applySmoothing,
          heightScale: values.heightScale
        }
      })).unwrap();

      setImportModalVisible(false);
      importForm.resetFields();
      message.success(t('terrain.importSuccess'));
    } catch (error: any) {
      message.error(t('terrain.importFailed') + ': ' + error.message);
    }
  };

  /**
   * 删除地形组件
   */
  const handleDeleteTerrain = () => {
    if (!selectedTerrainId) return;

    Modal.confirm({
      title: t('terrain.confirmDelete'),
      content: t('terrain.deleteWarning'),
      okText: t('common.confirm'),
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          // 调用引擎删除地形组件
          await terrainEngineService.deleteTerrainComponent(selectedTerrainId);

          dispatch(selectTerrain(null));

          if (onTerrainDeleted) {
            onTerrainDeleted(selectedTerrainId);
          }

          message.success(t('terrain.deleteSuccess'));
        } catch (error: any) {
          message.error(t('terrain.deleteFailed') + ': ' + error.message);
        }
      }
    });
  };

  /**
   * 导出地形
   */
  const handleExportTerrain = async () => {
    if (!selectedTerrainId) return;

    try {
      // 获取地形数据
      const terrainData = await terrainEngineService.exportTerrain(selectedTerrainId);

      // 创建下载链接
      const dataStr = JSON.stringify(terrainData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      // 创建下载链接并触发下载
      const link = document.createElement('a');
      link.href = url;
      link.download = `terrain_${selectedTerrainId}_${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      URL.revokeObjectURL(url);

      message.success(t('terrain.exportSuccess'));
    } catch (error: any) {
      message.error(t('terrain.exportFailed') + ': ' + error.message);
    }
  };

  /**
   * 处理地形修改
   */
  const handleTerrainModified = () => {
    // 地形修改后的回调
    console.log('地形已修改');
  };

  /**
   * 处理操作
   */
  const handleOperation = (operation: any) => {
    console.log('执行操作:', operation);
  };

  return (
    <div className="terrain-component-manager">
      <Card
        title={t('terrain.manager')}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t('terrain.create')}
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              {t('terrain.import')}
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportTerrain}
              disabled={!selectedTerrainId}
            >
              {t('terrain.export')}
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDeleteTerrain}
              disabled={!selectedTerrainId}
            >
              {t('terrain.delete')}
            </Button>
          </Space>
        }
      >
        {currentTerrain ? (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'generation',
                label: t('terrain.generation'),
                children: (
                  <TerrainGenerationTool
                    entityId={selectedTerrainId!}
                    onTerrainModified={handleTerrainModified}
                    onOperation={handleOperation}
                  />
                )
              },
              {
                key: 'sculpting',
                label: t('terrain.sculpting'),
                children: (
                  <TerrainSculptingTool
                    entityId={selectedTerrainId!}
                    onTerrainModified={handleTerrainModified}
                    onOperation={handleOperation}
                  />
                )
              },
              {
                key: 'preview',
                label: t('terrain.preview'),
                children: (
                  <TerrainPreview
                    algorithm="perlin"
                    params={{}}
                    width={300}
                    height={200}
                  />
                )
              }
            ]}
          />
        ) : (
          <div className="no-terrain-selected">
            <p>{t('terrain.noTerrainSelected')}</p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              {t('terrain.createFirst')}
            </Button>
          </div>
        )}
      </Card>

      {/* 创建地形模态框 */}
      <Modal
        title={t('terrain.createTerrain')}
        open={createModalVisible}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setCreateModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTerrain}
          initialValues={{
            width: 1000,
            height: 1000,
            resolution: 256,
            maxHeight: 100,
            useLOD: true,
            usePhysics: true
          }}
        >
          <Form.Item
            name="width"
            label={t('terrain.width')}
            rules={[{ required: true, message: t('terrain.widthRequired') as string }]}
          >
            <InputNumber min={100} max={10000} step={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="height"
            label={t('terrain.height')}
            rules={[{ required: true, message: t('terrain.heightRequired') as string }]}
          >
            <InputNumber min={100} max={10000} step={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="resolution"
            label={t('terrain.resolution')}
            rules={[{ required: true, message: t('terrain.resolutionRequired') as string }]}
          >
            <Select style={{ width: '100%' }}>
              <Option value={64}>64x64</Option>
              <Option value={128}>128x128</Option>
              <Option value={256}>256x256</Option>
              <Option value={512}>512x512</Option>
              <Option value={1024}>1024x1024</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="maxHeight"
            label={t('terrain.maxHeight')}
            rules={[{ required: true, message: t('terrain.maxHeightRequired') as string }]}
          >
            <InputNumber min={10} max={1000} step={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="useLOD" valuePropName="checked">
            <Switch /> {t('terrain.useLOD')}
          </Form.Item>

          <Form.Item name="usePhysics" valuePropName="checked">
            <Switch /> {t('terrain.usePhysics')}
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={isGenerating}>
                {t('terrain.create')}
              </Button>
              <Button onClick={() => setCreateModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入地形模态框 */}
      <Modal
        title={t('terrain.importTerrain')}
        open={importModalVisible}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setImportModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
      >
        <Form
          form={importForm}
          layout="vertical"
          onFinish={handleImportTerrain}
          initialValues={{
            applySmoothing: false,
            heightScale: 1.0
          }}
        >
          <Form.Item
            name="file"
            label={t('terrain.heightMapFile')}
            rules={[{ required: true, message: t('terrain.fileRequired') as string }]}
          >
            <Upload
              beforeUpload={() => false}
              accept=".png,.jpg,.jpeg,.raw,.r16,.r32"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>
                {t('terrain.selectFile')}
              </Button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="heightScale"
            label={t('terrain.heightScale')}
          >
            <InputNumber min={0.1} max={10} step={0.1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="applySmoothing" valuePropName="checked">
            <Switch /> {t('terrain.applySmoothing')}
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={isImporting}>
                {t('terrain.import')}
              </Button>
              <Button onClick={() => setImportModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
