/**
 * 场景图层面板
 * 用于管理场景中的图层
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  List,
  Button,
  Input,
  Space,
  Dropdown,
  Modal,
  Form,
  ColorPicker,
  Tag,
  Select,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EditOutlined,
  SearchOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons';

import EngineService from '../../services/EngineService';
import './LayersPanel.less';

// 图层数据接口
interface LayerData {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  order: number;
  color: string;
  tags: string[];
  entityCount: number;
  type: string;
  parentId: string | null;
  children: LayerData[];
  expanded: boolean;
  level: number;
}

const LayersPanel: React.FC = () => {
  const { t } = useTranslation();

  // 状态
  const [layers, setLayers] = useState<LayerData[]>([]);
  const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isCreateGroupModalVisible, setIsCreateGroupModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [groupForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 加载图层数据
  useEffect(() => {
    loadLayers();
  }, []);

  // 加载图层
  const loadLayers = () => {
    try {
      const scene = EngineService.getActiveScene();
      if (!scene) {
        setLayers([]);
        return;
      }

      // 暂时创建一些示例图层数据，因为引擎中还没有图层管理器
      const mockLayers: LayerData[] = [
        {
          id: 'default',
          name: '默认图层',
          visible: true,
          locked: false,
          order: 0,
          color: 'ffffff',
          tags: ['default'],
          entityCount: 0,
          type: 'layer',
          parentId: null,
          children: [],
          expanded: true,
          level: 0
        },
        {
          id: 'ui',
          name: 'UI图层',
          visible: true,
          locked: false,
          order: 1,
          color: '00ff00',
          tags: ['ui'],
          entityCount: 0,
          type: 'layer',
          parentId: null,
          children: [],
          expanded: true,
          level: 0
        }
      ];

      setLayers(mockLayers);
    } catch (error) {
      console.error('加载图层失败:', error);
      message.error('加载图层失败');
    }
  };

  // 过滤图层
  const filteredLayers = layers.filter(layer =>
    layer.name.toLowerCase().includes(searchValue.toLowerCase()) ||
    layer.tags.some(tag => tag.toLowerCase().includes(searchValue.toLowerCase()))
  );

  // 创建图层
  const handleCreateLayer = () => {
    form.validateFields().then(values => {
      try {
        // 暂时只是模拟创建图层
        const newLayer: LayerData = {
          id: `layer_${Date.now()}`,
          name: values.name,
          visible: true,
          locked: false,
          order: layers.length,
          tags: values.tags || [],
          color: values.color || 'ffffff',
          entityCount: 0,
          type: 'layer',
          parentId: values.parentId || null,
          children: [],
          expanded: true,
          level: 0
        };

        setLayers(prev => [...prev, newLayer]);

        // 关闭对话框
        setIsCreateModalVisible(false);
        form.resetFields();

        message.success(`图层 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建图层失败:', error);
        message.error('创建图层失败');
      }
    });
  };

  // 创建图层组
  const handleCreateLayerGroup = () => {
    groupForm.validateFields().then(values => {
      try {
        // 暂时只是模拟创建图层组
        const newGroup: LayerData = {
          id: `group_${Date.now()}`,
          name: values.name,
          visible: true,
          locked: false,
          order: layers.length,
          tags: [],
          color: 'cccccc',
          entityCount: 0,
          type: 'group',
          parentId: values.parentId || null,
          children: [],
          expanded: true,
          level: 0
        };

        setLayers(prev => [...prev, newGroup]);

        // 关闭对话框
        setIsCreateGroupModalVisible(false);
        groupForm.resetFields();

        message.success(`图层组 "${values.name}" 已创建`);
      } catch (error) {
        console.error('创建图层组失败:', error);
        message.error('创建图层组失败');
      }
    });
  };

  // 编辑图层
  const handleEditLayer = () => {
    if (!selectedLayerId) return;

    editForm.validateFields().then(values => {
      try {
        // 暂时只是模拟编辑图层
        setLayers(prev => prev.map(layer =>
          layer.id === selectedLayerId
            ? { ...layer, name: values.name, tags: values.tags || [], color: values.color || layer.color }
            : layer
        ));

        // 关闭对话框
        setIsEditModalVisible(false);
        editForm.resetFields();

        message.success(`图层 "${values.name}" 已更新`);
      } catch (error) {
        console.error('编辑图层失败:', error);
        message.error('编辑图层失败');
      }
    });
  };

  // 删除图层
  const handleDeleteLayer = (layerId: string) => {
    try {
      // 获取图层名称
      const layer = layers.find(l => l.id === layerId);
      const layerName = layer ? layer.name : layerId;

      // 确认删除
      Modal.confirm({
        title: t('editor.layers.confirmDelete'),
        content: t('editor.layers.confirmDeleteContent', { name: layerName }),
        okText: t('common.yes'),
        cancelText: t('common.no'),
        onOk: () => {
          // 删除图层
          setLayers(prev => prev.filter(l => l.id !== layerId));

          // 如果删除的是当前选中的图层，清除选择
          if (selectedLayerId === layerId) {
            setSelectedLayerId(null);
          }

          message.success(`图层 "${layerName}" 已删除`);
        }
      });
    } catch (error) {
      console.error('删除图层失败:', error);
      message.error('删除图层失败');
    }
  };

  // 切换图层可见性
  const handleToggleVisibility = (layerId: string, visible: boolean) => {
    try {
      // 更新状态
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId ? { ...l, visible: !visible } : l
        )
      );
    } catch (error) {
      console.error('切换图层可见性失败:', error);
    }
  };

  // 切换图层锁定状态
  const handleToggleLock = (layerId: string, locked: boolean) => {
    try {
      // 更新状态
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId ? { ...l, locked: !locked } : l
        )
      );
    } catch (error) {
      console.error('切换图层锁定状态失败:', error);
    }
  };

  // 移动图层顺序
  const handleMoveLayer = (layerId: string, direction: 'up' | 'down') => {
    try {
      // 找到当前图层和目标图层
      const currentIndex = layers.findIndex(l => l.id === layerId);
      if (currentIndex === -1) return;

      const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

      // 检查边界
      if (targetIndex < 0 || targetIndex >= layers.length) return;

      // 交换图层位置
      const newLayers = [...layers];
      [newLayers[currentIndex], newLayers[targetIndex]] = [newLayers[targetIndex], newLayers[currentIndex]];

      // 更新order属性
      newLayers.forEach((layer, index) => {
        layer.order = index;
      });

      setLayers(newLayers);
    } catch (error) {
      console.error('移动图层失败:', error);
    }
  };

  // 打开编辑对话框
  const handleOpenEditModal = (layerId: string) => {
    try {
      const layer = layers.find(l => l.id === layerId);
      if (!layer) return;

      // 设置表单初始值
      editForm.setFieldsValue({
        name: layer.name,
        tags: layer.tags,
        color: layer.color,
        parentId: layer.parentId
      });

      setSelectedLayerId(layerId);
      setIsEditModalVisible(true);
    } catch (error) {
      console.error('打开编辑对话框失败:', error);
    }
  };

  // 切换图层展开状态
  const handleToggleExpanded = (layerId: string) => {
    try {
      // 切换展开状态
      setLayers(prevLayers =>
        prevLayers.map(l =>
          l.id === layerId ? { ...l, expanded: !l.expanded } : l
        )
      );
    } catch (error) {
      console.error('切换图层展开状态失败:', error);
    }
  };

  return (
    <div className="layers-panel">
      <div className="panel-header">
        <h3>{t('editor.panels.layers')}</h3>
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            {t('editor.layers.add')}
          </Button>
          <Button
            size="small"
            icon={<FolderOutlined />}
            onClick={() => setIsCreateGroupModalVisible(true)}
          >
            {t('editor.layers.addGroup')}
          </Button>
        </Space>
      </div>

      <Input
        placeholder={t('editor.layers.search') as string}
        prefix={<SearchOutlined />}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="search-input"
      />

      <List
        className="layers-list"
        dataSource={filteredLayers}
        renderItem={(layer) => (
          <List.Item
            key={layer.id}
            className={`layer-item ${selectedLayerId === layer.id ? 'selected' : ''} ${layer.type === 'group' ? 'layer-group' : ''}`}
            onClick={() => setSelectedLayerId(layer.id)}
            style={{ paddingLeft: `${layer.level * 16 + 8}px` }}
          >
            {layer.type === 'group' && (
              <Button
                type="text"
                size="small"
                className="expand-button"
                icon={layer.expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleExpanded(layer.id);
                }}
              />
            )}
            {layer.type === 'group' ? (
              <div className="layer-icon">
                {layer.expanded ? <FolderOpenOutlined /> : <FolderOutlined />}
              </div>
            ) : (
              <div className="layer-color" style={{ backgroundColor: `#${layer.color}` }} />
            )}
            <div className="layer-info">
              <div className="layer-name">{layer.name}</div>
              <div className="layer-meta">
                {layer.tags.map(tag => (
                  <Tag key={tag} className="layer-tag">{tag}</Tag>
                ))}
                <span className="entity-count">
                  {layer.type === 'group'
                    ? `${layer.children.length} 个子图层`
                    : `${layer.entityCount} 个实体`}
                </span>
              </div>
            </div>
            <div className="layer-actions">
              <Button
                type="text"
                size="small"
                icon={layer.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleVisibility(layer.id, layer.visible);
                }}
              />
              <Button
                type="text"
                size="small"
                icon={layer.locked ? <LockOutlined /> : <UnlockOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleLock(layer.id, layer.locked);
                }}
              />
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'edit',
                      icon: <EditOutlined />,
                      label: t('editor.layers.edit'),
                      onClick: () => handleOpenEditModal(layer.id)
                    },
                    ...(layer.type === 'group' ? [
                      {
                        key: 'addLayer',
                        icon: <PlusOutlined />,
                        label: t('editor.layers.addLayerToGroup'),
                        onClick: () => {
                          setSelectedLayerId(layer.id);
                          form.setFieldsValue({ parentId: layer.id });
                          setIsCreateModalVisible(true);
                        }
                      },
                      {
                        key: 'addGroup',
                        icon: <FolderOutlined />,
                        label: t('editor.layers.addGroupToGroup'),
                        onClick: () => {
                          setSelectedLayerId(layer.id);
                          groupForm.setFieldsValue({ parentId: layer.id });
                          setIsCreateGroupModalVisible(true);
                        }
                      }
                    ] : []),
                    {
                      key: 'moveUp',
                      icon: <ArrowUpOutlined />,
                      label: t('editor.layers.moveUp'),
                      disabled: layers.indexOf(layer) === 0,
                      onClick: () => handleMoveLayer(layer.id, 'up')
                    },
                    {
                      key: 'moveDown',
                      icon: <ArrowDownOutlined />,
                      label: t('editor.layers.moveDown'),
                      disabled: layers.indexOf(layer) === layers.length - 1,
                      onClick: () => handleMoveLayer(layer.id, 'down')
                    },
                    {
                      type: 'divider' as const
                    },
                    {
                      key: 'delete',
                      icon: <DeleteOutlined />,
                      label: t('editor.layers.delete'),
                      danger: true,
                      disabled: layer.id === 'default' || layer.id === 'root',
                      onClick: () => handleDeleteLayer(layer.id)
                    }
                  ]
                }}
                trigger={['click']}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<MoreOutlined />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>
          </List.Item>
        )}
      />

      {/* 创建图层对话框 */}
      <Modal
        title={t('editor.layers.createLayer')}
        open={isCreateModalVisible}
        onOk={handleCreateLayer}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsCreateModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.name')}
            rules={[{ required: true, message: t('editor.layers.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group')
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="tags"
            label={t('editor.layers.tags')}
          >
            <Select mode="tags" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="color"
            label={t('editor.layers.color')}
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建图层组对话框 */}
      <Modal
        title={t('editor.layers.createGroup')}
        open={isCreateGroupModalVisible}
        onOk={handleCreateLayerGroup}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsCreateGroupModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <Form form={groupForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.groupName')}
            rules={[{ required: true, message: t('editor.layers.groupNameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group')
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑图层对话框 */}
      <Modal
        title={t('editor.layers.editLayer')}
        open={isEditModalVisible}
        onOk={handleEditLayer}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsEditModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('editor.layers.name')}
            rules={[{ required: true, message: t('editor.layers.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="parentId"
            label={t('editor.layers.parent')}
          >
            <Select
              allowClear
              placeholder={t('editor.layers.selectParent')}
              style={{ width: '100%' }}
            >
              {layers
                .filter(layer => layer.type === 'group' && layer.id !== selectedLayerId)
                .map(layer => (
                  <Select.Option key={layer.id} value={layer.id}>
                    {'　'.repeat(layer.level)}{layer.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="tags"
            label={t('editor.layers.tags')}
          >
            <Select mode="tags" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="color"
            label={t('editor.layers.color')}
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LayersPanel;
