/**
 * 上下文感知反馈按钮组件
 * 用于在编辑器中添加上下文感知反馈入口
 */
import React, { useState } from 'react';
import { But<PERSON>, Tooltip, Drawer, Badge, Popover, Modal } from 'antd';
import {
  CommentOutlined,
  BulbOutlined,
  BugOutlined,
  RocketOutlined,
  ToolOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ContextAwareFeedbackForm from './ContextAwareFeedbackForm';
import './FeedbackButton.less';

export interface ContextAwareFeedbackButtonProps {
  /** 反馈类型 */
  type: 'animation' | 'physics' | 'rendering' | 'editor' | 'general';
  /** 反馈子类型 */
  subType?: string;
  /** 相关实体ID */
  entityId?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 性能数据 */
  performanceData?: any;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'text' | 'link' | 'dashed';
  /** 按钮大小 */
  buttonSize?: 'large' | 'middle' | 'small';
  /** 是否显示文本 */
  showText?: boolean;
  /** 是否使用抽屉 */
  useDrawer?: boolean;
  /** 是否使用气泡卡片 */
  usePopover?: boolean;
  /** 是否使用模态框 */
  useModal?: boolean;
  /** 是否显示徽标 */
  showBadge?: boolean;
  /** 徽标数量 */
  badgeCount?: number;
  /** 是否自动捕获上下文 */
  autoCaptureContext?: boolean;
  /** 是否显示上下文提示 */
  showContextTooltip?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 上下文提示文本 */
  contextTooltip?: string;
}

/**
 * 上下文感知反馈按钮组件
 */
const ContextAwareFeedbackButton: React.FC<ContextAwareFeedbackButtonProps> = ({
  type,
  subType,
  entityId,
  resourceId,
  buttonType = 'default',
  buttonSize = 'middle',
  showText = true,
  useDrawer = true,
  usePopover = false,
  useModal = false,
  showBadge = false,
  badgeCount = 0,
  autoCaptureContext = true,
  showContextTooltip = true,
  style,
  className,
  contextTooltip
}) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);

  // 获取图标
  const getIcon = () => {
    switch (type) {
      case 'animation':
        return <RocketOutlined />;
      case 'physics':
        return <BugOutlined />;
      case 'rendering':
        return <BulbOutlined />;
      case 'editor':
        return <ToolOutlined />;
      default:
        return <CommentOutlined />;
    }
  };

  // 获取文本
  const getText = () => {
    switch (type) {
      case 'animation':
        return t('feedback.button.animation') || '动画反馈';
      case 'physics':
        return t('feedback.button.physics') || '物理反馈';
      case 'rendering':
        return t('feedback.button.rendering') || '渲染反馈';
      case 'editor':
        return t('feedback.button.editor') || '编辑器反馈';
      default:
        return t('feedback.button.general') || '通用反馈';
    }
  };

  // 捕获上下文
  const captureContext = () => {
    try {
      // 组合上下文数据
      const contextualData = {
        timestamp: new Date().toISOString(),
        location: window.location.href,
        userAgent: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        type,
        subType,
        entityId,
        resourceId
      };

      return contextualData;
    } catch (error) {
      console.error('捕获上下文失败:', error);
      return null;
    }
  };

  // 打开反馈表单
  const showFeedback = () => {
    if (autoCaptureContext) {
      captureContext();
    }
    setVisible(true);
  };

  // 关闭反馈表单
  const closeFeedback = () => {
    setVisible(false);
  };

  // 提交成功回调
  const handleSuccess = () => {
    // 可以在这里添加提交成功后的逻辑
  };

  // 渲染反馈表单
  const renderFeedbackForm = () => {
    return (
      <ContextAwareFeedbackForm
        type={type}
        subType={subType}
        entityId={entityId}
        resourceId={resourceId}
        onClose={closeFeedback}
        onSuccess={handleSuccess}
        autoCaptureContext={autoCaptureContext}
      />
    );
  };

  // 获取上下文提示文本
  const getContextTooltip = () => {
    if (contextTooltip) {
      return contextTooltip;
    }

    return t('feedback.contextTooltip') || '上下文感知反馈';
  };

  // 渲染按钮
  const renderButton = () => {
    const button = (
      <Button
        type={buttonType}
        size={buttonSize}
        icon={getIcon()}
        onClick={showFeedback}
        className={`feedback-button ${className || ''}`}
        style={style}
      >
        {showText && getText()}
        {showContextTooltip && (
          <Tooltip title={getContextTooltip()} placement="right">
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        )}
      </Button>
    );

    // 如果需要显示徽标
    if (showBadge) {
      return (
        <Badge count={badgeCount} dot={badgeCount === 0}>
          {button}
        </Badge>
      );
    }

    return button;
  };

  // 如果使用气泡卡片
  if (usePopover) {
    return (
      <Popover
        content={renderFeedbackForm()}
        title={t('feedback.title.general') || '反馈'}
        trigger="click"
        open={visible}
        onOpenChange={setVisible}
      >
        <Tooltip title={t('feedback.tooltip') || '点击提供反馈'}>
          {renderButton()}
        </Tooltip>
      </Popover>
    );
  }

  // 如果使用抽屉
  if (useDrawer) {
    return (
      <>
        <Tooltip title={t('feedback.tooltip') || '点击提供反馈'}>
          {renderButton()}
        </Tooltip>

        <Drawer
          title={t('feedback.title.general') || '反馈'}
          placement="right"
          onClose={closeFeedback}
          open={visible}
          width={600}
        >
          {renderFeedbackForm()}
        </Drawer>
      </>
    );
  }

  // 如果使用模态框
  if (useModal) {
    return (
      <>
        <Tooltip title={t('feedback.tooltip') || '点击提供反馈'}>
          {renderButton()}
        </Tooltip>

        <Modal
          title={t('feedback.title.general') || '反馈'}
          open={visible}
          onCancel={closeFeedback}
          footer={null}
          width={700}
         destroyOnClose keyboard={true} maskClosable={true}>
          {renderFeedbackForm()}
        </Modal>
      </>
    );
  }

  // 默认使用模态框
  return (
    <>
      <Tooltip title={t('feedback.tooltip') || '点击提供反馈'}>
        {renderButton()}
      </Tooltip>

      {visible && renderFeedbackForm()}
    </>
  );
};

export default ContextAwareFeedbackButton;
