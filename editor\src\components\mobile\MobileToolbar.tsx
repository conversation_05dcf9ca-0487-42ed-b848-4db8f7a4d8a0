/**
 * 移动工具栏组件
 * 提供移动设备优化的工具栏
 */
import React, { useState, useEffect } from 'react';
import { But<PERSON>, Tooltip, Drawer, Space, Menu, Badge, Switch} from 'antd';
import {
  MenuOutlined,
  EditOutlined,
  UndoOutlined,
  RedoOutlined,
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  WifiOutlined,
  ThunderboltOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  undo,
  redo
} from '../../store/editor/editorSlice';
import { toggleFullscreen } from '../../store/ui/uiSlice';
import MobileDeviceService, { ScreenOrientation } from '../../services/MobileDeviceService';
import MobilePerformanceService, { PerformanceLevel } from '../../services/MobilePerformanceService';
import MobileNetworkService, { NetworkQualityLevel } from '../../services/MobileNetworkService';
import './MobileToolbar.less';

// 工具栏项目接口
interface ToolbarItem {
  key: string;
  icon: React.ReactNode;
  title: string;
  action: () => void;
  visible?: boolean;
  badge?: number | null;
  status?: 'success' | 'processing' | 'error' | 'default';
}

/**
 * 移动工具栏组件
 */
const MobileToolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  // 从Redux获取状态
  const {
    transformMode,
    showGrid,
    showAxes,
    isPlaying} = useAppSelector((state) => state.editor);
  
  const { fullscreen } = useAppSelector((state) => state.ui);
  
  // 本地状态
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [batteryLevel, setBatteryLevel] = useState(100);
  const [isCharging, setIsCharging] = useState(false);
  const [networkQuality, setNetworkQuality] = useState(NetworkQualityLevel.GOOD);
  const [performanceLevel, setPerformanceLevel] = useState(PerformanceLevel.AUTO);
  const [orientation, setOrientation] = useState(ScreenOrientation.LANDSCAPE);
  
  // 初始化
  useEffect(() => {
    // 获取设备信息
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setOrientation(deviceInfo.orientation);

    // 监听设备变化
    MobileDeviceService.on('orientationChanged', (info: any) => {
      setOrientation(info.orientation);
    });
    
    // 获取性能级别
    const perfLevel = MobilePerformanceService.getPerformanceLevel();
    setPerformanceLevel(perfLevel);
    
    // 监听性能变化
    MobilePerformanceService.on('performanceLevelChanged', (data: any) => {
      setPerformanceLevel(data.newLevel);
    });
    
    // 获取电池信息
    const monitorData = MobilePerformanceService.getMonitorData();
    setBatteryLevel(monitorData.batteryLevel);
    setIsCharging(monitorData.isCharging);
    
    // 监听电池变化
    MobilePerformanceService.on('performanceMonitorUpdate', (data: any) => {
      setBatteryLevel(data.batteryLevel);
      setIsCharging(data.isCharging);
    });
    
    // 获取网络质量
    const networkStatus = MobileNetworkService.getNetworkStatus();
    setNetworkQuality(networkStatus.qualityLevel);
    
    // 监听网络变化
    MobileNetworkService.on('networkQualityChanged', (data: any) => {
      setNetworkQuality(data.newLevel);
    });
    
    return () => {
      // 清理事件监听
      MobileDeviceService.removeAllListeners();
      MobilePerformanceService.removeAllListeners();
      MobileNetworkService.removeAllListeners();
    };
  }, []);
  
  // 工具栏项目
  const toolbarItems: ToolbarItem[] = [
    {
      key: 'menu',
      icon: <MenuOutlined />,
      title: t('editor.mobile.menu'),
      action: () => setDrawerVisible(true)},
    {
      key: 'translate',
      icon: <EditOutlined />,
      title: t('editor.mobile.translate'),
      action: () => dispatch(setTransformMode(TransformMode.TRANSLATE)),
      badge: transformMode === TransformMode.TRANSLATE ? 1 : null,
      status: transformMode === TransformMode.TRANSLATE ? 'processing' : 'default'
    },
    {
      key: 'undo',
      icon: <UndoOutlined />,
      title: t('editor.mobile.undo'),
      action: () => dispatch(undo())},
    {
      key: 'redo',
      icon: <RedoOutlined />,
      title: t('editor.mobile.redo'),
      action: () => dispatch(redo())},
    {
      key: 'play',
      icon: isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />,
      title: isPlaying ? t('editor.mobile.pause') : t('editor.mobile.play'),
      action: () => dispatch(setIsPlaying(!isPlaying))},
    {
      key: 'fullscreen',
      icon: fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />,
      title: fullscreen ? t('editor.mobile.exitFullscreen') : t('editor.mobile.fullscreen'),
      action: () => dispatch(toggleFullscreen())},
    {
      key: 'more',
      icon: <MoreOutlined />,
      title: t('editor.mobile.more'),
      action: () => setDrawerVisible(true)},
  ];
  
  // 获取网络质量图标
  const getNetworkQualityIcon = () => {
    switch (networkQuality) {
      case NetworkQualityLevel.EXCELLENT:
        return <Badge status="success" dot><WifiOutlined /></Badge>;
      case NetworkQualityLevel.GOOD:
        return <Badge status="success" dot><WifiOutlined /></Badge>;
      case NetworkQualityLevel.FAIR:
        return <Badge status="warning" dot><WifiOutlined /></Badge>;
      case NetworkQualityLevel.POOR:
        return <Badge status="error" dot><WifiOutlined /></Badge>;
      default:
        return <Badge status="default" dot><WifiOutlined /></Badge>;
    }
  };
  
  // 获取电池图标
  const getBatteryIcon = () => {
    if (isCharging) {
      return <Badge status="success" dot><ThunderboltOutlined /></Badge>;
    }

    if (batteryLevel <= 20) {
      return <Badge status="error" dot><ThunderboltOutlined /></Badge>;
    }

    if (batteryLevel <= 50) {
      return <Badge status="warning" dot><ThunderboltOutlined /></Badge>;
    }

    return <Badge status="success" dot><ThunderboltOutlined /></Badge>;
  };
  
  // 渲染工具栏
  return (
    <div className={`mobile-toolbar ${orientation === ScreenOrientation.PORTRAIT ? 'portrait' : 'landscape'}`}>
      <div className="toolbar-main">
        <Space size="middle">
          {toolbarItems.map((item) => (
            <Tooltip key={item.key} title={item.title} placement="bottom">
              <Badge count={item.badge} status={item.status} dot={item.status !== 'default'}>
                <Button
                  type={item.key === 'menu' ? 'primary' : 'default'}
                  shape="circle"
                  icon={item.icon}
                  onClick={item.action}
                  className="toolbar-button"
                />
              </Badge>
            </Tooltip>
          ))}
        </Space>
      </div>
      
      <div className="toolbar-status">
        <Space size="small">
          <Tooltip title={t('editor.mobile.networkStatus')}>
            {getNetworkQualityIcon()}
          </Tooltip>
          <Tooltip title={`${t('editor.mobile.batteryLevel')}: ${batteryLevel}%${isCharging ? ' (' + t('editor.mobile.charging') + ')' : ''}`}>
            {getBatteryIcon()}
          </Tooltip>
        </Space>
      </div>
      
      {/* 侧边菜单抽屉 */}
      <Drawer
        title={t('editor.mobile.menu')}
        placement={orientation === ScreenOrientation.PORTRAIT ? 'left' : 'bottom'}
        onClose={() => {
          try {
            setDrawerVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        open={drawerVisible}
        width={orientation === ScreenOrientation.PORTRAIT ? 300 : '100%'}
        height={orientation === ScreenOrientation.PORTRAIT ? '100%' : 300}
        styles={{
          body: { padding: 0 },
          header: { padding: '10px 16px' }
        }}
      >
        <Menu mode="vertical">
          <Menu.Item key="grid" icon={showGrid ? <EyeOutlined /> : <EyeInvisibleOutlined />}>
            <Space>
              {t('editor.mobile.showGrid')}
              <Switch size="small" checked={showGrid} onChange={(checked) => dispatch(setShowGrid(checked))} />
            </Space>
          </Menu.Item>
          <Menu.Item key="axes" icon={showAxes ? <EyeOutlined /> : <EyeInvisibleOutlined />}>
            <Space>
              {t('editor.mobile.showAxes')}
              <Switch size="small" checked={showAxes} onChange={(checked) => dispatch(setShowAxes(checked))} />
            </Space>
          </Menu.Item>
          <Menu.Divider />
          <Menu.SubMenu key="performance" icon={<SettingOutlined />} title={t('editor.mobile.performance')}>
            <Menu.Item key="perf-auto">
              <Space>
                {t('editor.mobile.performanceAuto')}
                <Switch size="small" checked={performanceLevel === PerformanceLevel.AUTO} onChange={(checked) => {
                  if (checked) {
                    MobilePerformanceService.setPerformanceLevel(PerformanceLevel.AUTO);
                  }
                }} />
              </Space>
            </Menu.Item>
            <Menu.Item key="perf-low">
              <Space>
                {t('editor.mobile.performanceLow')}
                <Switch size="small" checked={performanceLevel === PerformanceLevel.LOW} onChange={(checked) => {
                  if (checked) {
                    MobilePerformanceService.setPerformanceLevel(PerformanceLevel.LOW);
                  }
                }} />
              </Space>
            </Menu.Item>
            <Menu.Item key="perf-medium">
              <Space>
                {t('editor.mobile.performanceMedium')}
                <Switch size="small" checked={performanceLevel === PerformanceLevel.MEDIUM} onChange={(checked) => {
                  if (checked) {
                    MobilePerformanceService.setPerformanceLevel(PerformanceLevel.MEDIUM);
                  }
                }} />
              </Space>
            </Menu.Item>
            <Menu.Item key="perf-high">
              <Space>
                {t('editor.mobile.performanceHigh')}
                <Switch size="small" checked={performanceLevel === PerformanceLevel.HIGH} onChange={(checked) => {
                  if (checked) {
                    MobilePerformanceService.setPerformanceLevel(PerformanceLevel.HIGH);
                  }
                }} />
              </Space>
            </Menu.Item>
          </Menu.SubMenu>
        </Menu>
      </Drawer>
    </div>
  );
};

export default MobileToolbar;
