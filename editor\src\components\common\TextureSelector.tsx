/**
 * 纹理选择器组件
 * 用于选择纹理
 */
import React, { useState, useEffect } from 'react';
import { Button, Input, Modal, Space, Empty, Card, Row, Col } from 'antd';
import { FolderOpenOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { Asset, AssetType } from '../../store/asset/assetSlice';

/**
 * 纹理选择器组件属性
 */
interface TextureSelectorProps {
  /** 值 */
  value?: string | null;
  /** 变更回调 */
  onChange?: (value: string | null) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 纹理类型 */
  textureType?: 'diffuse' | 'normal' | 'roughness' | 'metalness' | 'emissive' | 'ao' | 'height' | 'alpha' | 'all';
}

/**
 * 纹理选择器组件
 */
const TextureSelector: React.FC<TextureSelectorProps> = ({
  value = null,
  onChange,
  disabled = false,
  textureType = 'all'
}) => {
  const { t } = useTranslation();
  const [texturePath, setTexturePath] = useState<string | null>(value);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  
  // 从Redux获取资产列表并过滤出纹理
  const assets = useSelector((state: RootState) => state.asset.assets);
  const textures = assets.filter((asset: Asset) => asset.type === AssetType.TEXTURE);

  // 当值变化时更新内部状态
  useEffect(() => {
    setTexturePath(value);
  }, [value]);

  // 处理纹理变更
  const handleTextureChange = (path: string | null) => {
    setTexturePath(path);

    if (onChange) {
      onChange(path);
    }

    setModalVisible(false);
  };

  // 处理输入框变更
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const path = e.target.value || null;
    setTexturePath(path);

    if (onChange) {
      onChange(path);
    }
  };

  // 处理清除
  const handleClear = () => {
    setTexturePath(null);

    if (onChange) {
      onChange(null);
    }
  };

  // 处理预览
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 过滤纹理列表
  const filteredTextures = textures.filter((texture: Asset) => {
    if (textureType === 'all') {
      return true;
    }

    // 根据纹理类型过滤
    switch (textureType) {
      case 'diffuse':
        return texture.name.toLowerCase().includes('diffuse') ||
               texture.name.toLowerCase().includes('albedo') ||
               texture.name.toLowerCase().includes('color');
      case 'normal':
        return texture.name.toLowerCase().includes('normal');
      case 'roughness':
        return texture.name.toLowerCase().includes('roughness');
      case 'metalness':
        return texture.name.toLowerCase().includes('metalness') ||
               texture.name.toLowerCase().includes('metallic');
      case 'emissive':
        return texture.name.toLowerCase().includes('emissive') ||
               texture.name.toLowerCase().includes('emission');
      case 'ao':
        return texture.name.toLowerCase().includes('ao') ||
               texture.name.toLowerCase().includes('ambient');
      case 'height':
        return texture.name.toLowerCase().includes('height') ||
               texture.name.toLowerCase().includes('displacement');
      case 'alpha':
        return texture.name.toLowerCase().includes('alpha') ||
               texture.name.toLowerCase().includes('opacity');
      default:
        return true;
    }
  });
  
  // 渲染纹理选择器内容
  const textureSelectorContent = (
    <div style={{ maxHeight: 400, overflowY: 'auto' }}>
      {filteredTextures.length === 0 ? (
        <Empty description={t('editor.common.noTextures')} />
      ) : (
        <Row gutter={[16, 16]}>
          {filteredTextures.map((texture: Asset) => (
            <Col span={6} key={texture.id}>
              <Card
                hoverable
                cover={<img alt={texture.name} src={texture.thumbnail || texture.url} style={{ height: 100, objectFit: 'cover' }} />}
                onClick={() => handleTextureChange(texture.url)}
                style={{ cursor: 'pointer' }}
              >
                <Card.Meta title={texture.name} description={texture.type} />
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
  
  return (
    <div>
      <Space>
        <Input
          value={texturePath || ''}
          onChange={handleInputChange}
          disabled={disabled}
          style={{ width: 200 }}
          placeholder={String(t('editor.common.texturePath'))}
        />
        <Button
          icon={<FolderOpenOutlined />}
          onClick={() => setModalVisible(true)}
          disabled={disabled}
          title={String(t('editor.common.browse'))}
        />
        {texturePath && (
          <>
            <Button
              icon={<EyeOutlined />}
              onClick={handlePreview}
              disabled={disabled}
              title={String(t('editor.common.preview'))}
            />
            <Button
              icon={<DeleteOutlined />}
              onClick={handleClear}
              disabled={disabled}
              danger
              title={String(t('editor.common.clear'))}
            />
          </>
        )}
      </Space>
      
      <Modal
        title={t('editor.common.selectTexture')}
        open={modalVisible}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
        width={800}
      >
        {textureSelectorContent}
      </Modal>
      
      <Modal
        title={t('editor.common.texturePreview')}
        open={previewVisible}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setPreviewVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
      >
        {texturePath && (
          <div style={{ textAlign: 'center' }}>
            <img
              alt={String(t('editor.common.texturePreview'))}
              src={texturePath}
              style={{ maxWidth: '100%', maxHeight: 500 }}
            />
            <p>{texturePath}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TextureSelector;
