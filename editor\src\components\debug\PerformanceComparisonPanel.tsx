/**
 * 性能对比面板组件
 * 用于比较不同性能快照之间的差异
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, Button, Table, Tabs, Empty, Statistic, Space, Typography, Divider, Tag, Modal, Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
// 移除 @ant-design/charts 导入，使用简单的图表替代
// import { Line, Column, DualAxes } from '@ant-design/charts';
import {
  SwapOutlined as CompareOutlined, // 使用 SwapOutlined 替代 CompareOutlined
  SaveOutlined,
  DeleteOutlined,
  PlusOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined} from '@ant-design/icons';
import { PerformanceComparisonService, PerformanceSnapshot, PerformanceComparisonResult, PerformanceComparisonEventType, PerformanceMetricType } from '../../services/PerformanceComparisonService';
import './PerformanceComparisonPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;

// 模拟性能监控器
class PerformanceMonitorConfigConfig {
  private static instance: PerformanceMonitorConfigConfig;

  static getInstance() {
    if (!this.instance) {
      this.instance = new PerformanceMonitorConfigConfig();
    }
    return this.instance;
  }

  getReport() {
    return {
      timestamp: Date.now(),
      metrics: {
        [PerformanceMetricType.FPS]: {
          type: PerformanceMetricType.FPS,
          name: 'FPS',
          value: 60,
          min: 45,
          max: 60,
          average: 58,
          history: [60, 59, 58, 60],
          historyLimit: 60
        },
        [PerformanceMetricType.MEMORY_USAGE]: {
          type: PerformanceMetricType.MEMORY_USAGE,
          name: '内存使用',
          value: 512,
          min: 400,
          max: 600,
          average: 500,
          history: [512, 510, 515, 512],
          historyLimit: 60
        },
        [PerformanceMetricType.RENDER_TIME]: {
          type: PerformanceMetricType.RENDER_TIME,
          name: '渲染时间',
          value: 16.7,
          min: 15.0,
          max: 18.0,
          average: 16.5,
          history: [16.7, 16.5, 16.8, 16.7],
          historyLimit: 60
        },
        [PerformanceMetricType.CPU_USAGE]: {
          type: PerformanceMetricType.CPU_USAGE,
          name: 'CPU使用率',
          value: 45,
          min: 30,
          max: 60,
          average: 45,
          history: [45, 44, 46, 45],
          historyLimit: 60
        },
        [PerformanceMetricType.GPU_USAGE]: {
          type: PerformanceMetricType.GPU_USAGE,
          name: 'GPU使用率',
          value: 60,
          min: 50,
          max: 70,
          average: 60,
          history: [60, 59, 61, 60],
          historyLimit: 60
        }
      },
      bottlenecks: [],
      trends: [],
      overallScore: 85,
      status: 'good' as const
    };
  }
}

// 格式化数值，保留两位小数
const formatNumber = (value: number): string => {
  return value.toFixed(2);
};

// 格式化百分比变化
const formatPercentChange = (value: number): string => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
};

// 获取变化状态颜色
const getChangeStatusColor = (value: number, isImprovement: boolean): string => {
  if (Math.abs(value) < 1) {
    return '#d9d9d9'; // 变化不明显，灰色
  }
  return isImprovement ? '#52c41a' : '#f5222d';
};

interface PerformanceComparisonPanelProps {
  className?: string;
}

/**
 * 性能对比面板组件
 */
const PerformanceComparisonPanel: React.FC<PerformanceComparisonPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 状态
  const [snapshots, setSnapshots] = useState<PerformanceSnapshot[]>([]);
  const [baseSnapshotId, setBaseSnapshotId] = useState<string>('');
  const [compareSnapshotId, setCompareSnapshotId] = useState<string>('');
  const [comparisonResult, setComparisonResult] = useState<PerformanceComparisonResult | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isCreatingSnapshot, setIsCreatingSnapshot] = useState<boolean>(false);
  const [newSnapshotForm] = Form.useForm();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState<boolean>(false);
  const [snapshotToDelete, setSnapshotToDelete] = useState<string>('');
  
  // 服务实例
  const comparisonService = PerformanceComparisonService.getInstance();
  
  // 加载快照
  useEffect(() => {
    loadSnapshots();
    
    // 添加事件监听器
    comparisonService.on(PerformanceComparisonEventType.SNAPSHOT_CREATED, handleSnapshotCreated);
    comparisonService.on(PerformanceComparisonEventType.SNAPSHOT_DELETED, handleSnapshotDeleted);
    comparisonService.on(PerformanceComparisonEventType.COMPARISON_COMPLETED, handleComparisonCompleted);
    
    return () => {
      // 移除事件监听器
      comparisonService.off(PerformanceComparisonEventType.SNAPSHOT_CREATED, handleSnapshotCreated);
      comparisonService.off(PerformanceComparisonEventType.SNAPSHOT_DELETED, handleSnapshotDeleted);
      comparisonService.off(PerformanceComparisonEventType.COMPARISON_COMPLETED, handleComparisonCompleted);
    };
  }, []);
  
  // 加载快照
  const loadSnapshots = () => {
    const allSnapshots = comparisonService.getAllSnapshots();
    setSnapshots(allSnapshots);
    
    // 如果有快照，默认选择最新的两个
    if (allSnapshots.length >= 2) {
      setBaseSnapshotId(allSnapshots[1].id);
      setCompareSnapshotId(allSnapshots[0].id);
    } else if (allSnapshots.length === 1) {
      setBaseSnapshotId(allSnapshots[0].id);
    }
  };
  
  // 处理快照创建事件
  const handleSnapshotCreated = (_snapshot: PerformanceSnapshot) => {
    loadSnapshots();
  };
  
  // 处理快照删除事件
  const handleSnapshotDeleted = (snapshotId: string) => {
    loadSnapshots();
    
    // 如果删除的是当前选中的快照，重置选择
    if (snapshotId === baseSnapshotId) {
      setBaseSnapshotId('');
    }
    if (snapshotId === compareSnapshotId) {
      setCompareSnapshotId('');
    }
    
    // 如果删除的快照是比较结果中的快照，清除比较结果
    if (comparisonResult && (snapshotId === comparisonResult.baseSnapshot.id || snapshotId === comparisonResult.compareSnapshot.id)) {
      setComparisonResult(null);
    }
  };
  
  // 处理比较完成事件
  const handleComparisonCompleted = (result: PerformanceComparisonResult) => {
    setComparisonResult(result);
    setActiveTab('overview');
  };
  
  // 创建快照
  const createSnapshot = () => {
    setIsCreatingSnapshot(true);
    newSnapshotForm.resetFields();
  };
  
  // 处理快照创建提交
  const handleSnapshotCreateSubmit = (values: any) => {
    const { name, description, tags } = values;
    
    // 获取当前性能报告
    const report = PerformanceMonitorConfigConfig.getInstance().getReport();
    
    // 创建快照
    comparisonService.createSnapshot(
      name,
      report,
      description,
      tags ? tags.split(',').map((tag: string) => tag.trim()) : undefined
    );
    
    setIsCreatingSnapshot(false);
  };
  
  // 删除快照
  const confirmDeleteSnapshot = (snapshotId: string) => {
    setSnapshotToDelete(snapshotId);
    setIsDeleteModalVisible(true);
  };
  
  // 处理快照删除确认
  const handleDeleteConfirm = () => {
    if (snapshotToDelete) {
      comparisonService.deleteSnapshot(snapshotToDelete);
      setSnapshotToDelete('');
    }
    setIsDeleteModalVisible(false);
  };
  
  // 比较快照
  const compareSnapshots = () => {
    if (baseSnapshotId && compareSnapshotId) {
      comparisonService.compareSnapshots(baseSnapshotId, compareSnapshotId);
    }
  };
  
  // 渲染快照选择器
  const renderSnapshotSelector = () => {
    return (
      <div className="snapshot-selector">
        <Row gutter={16} align="middle">
          <Col span={10}>
            <Select
              placeholder={t('debug.performance.comparison.selectBaseSnapshot')}
              style={{ width: '100%' }}
              value={baseSnapshotId || undefined}
              onChange={setBaseSnapshotId}
            >
              {snapshots.map(snapshot => (
                <Option key={snapshot.id} value={snapshot.id}>
                  {snapshot.name} ({new Date(snapshot.timestamp).toLocaleString()})
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4} style={{ textAlign: 'center' }}>
            <CompareOutlined style={{ fontSize: 24 }} />
          </Col>
          <Col span={10}>
            <Select
              placeholder={t('debug.performance.comparison.selectCompareSnapshot')}
              style={{ width: '100%' }}
              value={compareSnapshotId || undefined}
              onChange={setCompareSnapshotId}
            >
              {snapshots.map(snapshot => (
                <Option key={snapshot.id} value={snapshot.id}>
                  {snapshot.name} ({new Date(snapshot.timestamp).toLocaleString()})
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col span={24} style={{ textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                icon={<CompareOutlined />}
                onClick={compareSnapshots}
                disabled={!baseSnapshotId || !compareSnapshotId || baseSnapshotId === compareSnapshotId}
              >
                {t('debug.performance.comparison.compare')}
              </Button>
              <Button
                icon={<SaveOutlined />}
                onClick={createSnapshot}
              >
                {t('debug.performance.comparison.createSnapshot')}
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染概览标签页
  const renderOverviewTab = () => {
    if (!comparisonResult) {
      return (
        <Empty
          description={t('debug.performance.comparison.noComparisonData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    const { scoreChange, metricChanges } = comparisonResult;
    
    // 关键指标
    const keyMetrics = [
      PerformanceMetricType.FPS,
      PerformanceMetricType.MEMORY_USAGE,
      PerformanceMetricType.RENDER_TIME,
      PerformanceMetricType.DRAW_CALLS,
    ];
    
    return (
      <div className="comparison-overview">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card>
              <Statistic
                title={t('debug.performance.comparison.scoreChange')}
                value={scoreChange.percentChange}
                precision={2}
                valueStyle={{
                  color: getChangeStatusColor(scoreChange.percentChange, scoreChange.percentChange > 0)}}
                prefix={scoreChange.percentChange > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                suffix="%"
              />
              <div className="score-details">
                <Text>{t('debug.performance.comparison.baseScore')}: {formatNumber(scoreChange.baseScore)}</Text>
                <Text>{t('debug.performance.comparison.compareScore')}: {formatNumber(scoreChange.compareScore)}</Text>
              </div>
            </Card>
          </Col>
        </Row>
        
        <Title level={4} style={{ marginTop: 16 }}>{t('debug.performance.comparison.keyMetrics')}</Title>
        <Row gutter={[16, 16]}>
          {keyMetrics.map(metricType => {
            const change = metricChanges[metricType];
            if (!change) return null;
            
            return (
              <Col span={6} key={metricType}>
                <Card>
                  <Statistic
                    title={t(`debug.performance.${metricType}`)}
                    value={change.percentChange}
                    precision={2}
                    valueStyle={{
                      color: getChangeStatusColor(change.percentChange, change.isImprovement)}}
                    prefix={change.isImprovement ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    suffix="%"
                  />
                  <div className="metric-details">
                    <Text>{t('debug.performance.comparison.before')}: {formatNumber(change.baseValue)}</Text>
                    <Text>{t('debug.performance.comparison.after')}: {formatNumber(change.compareValue)}</Text>
                  </div>
                </Card>
              </Col>
            );
          })}
        </Row>
        
        <Title level={4} style={{ marginTop: 16 }}>{t('debug.performance.comparison.bottleneckChanges')}</Title>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card title={t('debug.performance.comparison.resolvedBottlenecks')}>
              {comparisonResult.bottleneckChanges.resolved.length > 0 ? (
                <ul className="bottleneck-list">
                  {comparisonResult.bottleneckChanges.resolved.map(type => (
                    <li key={type}>
                      <CheckCircleOutlined style={{ color: '#52c41a' }} /> {t(`debug.performance.bottleneck.${type}`)}
                    </li>
                  ))}
                </ul>
              ) : (
                <Empty
                  description={t('debug.performance.comparison.noResolvedBottlenecks')}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Card>
          </Col>
          <Col span={8}>
            <Card title={t('debug.performance.comparison.newBottlenecks')}>
              {comparisonResult.bottleneckChanges.added.length > 0 ? (
                <ul className="bottleneck-list">
                  {comparisonResult.bottleneckChanges.added.map(type => (
                    <li key={type}>
                      <CloseCircleOutlined style={{ color: '#f5222d' }} /> {t(`debug.performance.bottleneck.${type}`)}
                    </li>
                  ))}
                </ul>
              ) : (
                <Empty
                  description={t('debug.performance.comparison.noNewBottlenecks')}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Card>
          </Col>
          <Col span={8}>
            <Card title={t('debug.performance.comparison.changedBottlenecks')}>
              {comparisonResult.bottleneckChanges.changed.length > 0 ? (
                <ul className="bottleneck-list">
                  {comparisonResult.bottleneckChanges.changed.map(change => (
                    <li key={change.type}>
                      {change.change < 0 ? (
                        <ArrowDownOutlined style={{ color: '#52c41a' }} />
                      ) : (
                        <ArrowUpOutlined style={{ color: '#f5222d' }} />
                      )}
                      {' '}
                      {t(`debug.performance.bottleneck.${change.type}`)}
                      {' '}
                      ({formatPercentChange(change.change * 100)})
                    </li>
                  ))}
                </ul>
              ) : (
                <Empty
                  description={t('debug.performance.comparison.noChangedBottlenecks')}
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  // 渲染指标标签页
  const renderMetricsTab = () => {
    if (!comparisonResult) {
      return (
        <Empty
          description={t('debug.performance.comparison.noComparisonData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    const { metricChanges } = comparisonResult;
    const metricTypes = Object.keys(metricChanges) as PerformanceMetricType[];
    
    // 按类别分组指标
    const renderingMetrics = metricTypes.filter(type => [
      PerformanceMetricType.FPS,
      PerformanceMetricType.RENDER_TIME,
      PerformanceMetricType.DRAW_CALLS,
      PerformanceMetricType.TRIANGLES,
      PerformanceMetricType.VERTICES,
    ].includes(type));
    
    const memoryMetrics = metricTypes.filter(type => [
      PerformanceMetricType.MEMORY_USAGE,
      PerformanceMetricType.TEXTURE_MEMORY,
      PerformanceMetricType.GEOMETRY_MEMORY,
      PerformanceMetricType.GPU_MEMORY,
      PerformanceMetricType.RESOURCE_MEMORY,
    ].includes(type));
    
    const systemMetrics = metricTypes.filter(type => [
      PerformanceMetricType.CPU_USAGE,
      PerformanceMetricType.GPU_USAGE,
      PerformanceMetricType.ENTITY_COUNT,
      PerformanceMetricType.COMPONENT_COUNT,
      PerformanceMetricType.SYSTEM_COUNT,
      PerformanceMetricType.SCRIPT_TIME,
      PerformanceMetricType.TOTAL_UPDATE_TIME,
    ].includes(type));
    
    const physicsMetrics = metricTypes.filter(type => [
      PerformanceMetricType.PHYSICS_TIME,
      PerformanceMetricType.COLLISION_PAIRS,
      PerformanceMetricType.CONTACT_POINTS,
    ].includes(type));
    
    const otherMetrics = metricTypes.filter(type => 
      !renderingMetrics.includes(type) && 
      !memoryMetrics.includes(type) && 
      !systemMetrics.includes(type) && 
      !physicsMetrics.includes(type)
    );
    
    // 表格列定义
    const columns = [
      {
        title: t('debug.performance.comparison.metric'),
        dataIndex: 'metric',
        key: 'metric',
        render: (text: string) => t(`debug.performance.${text}`)},
      {
        title: t('debug.performance.comparison.before'),
        dataIndex: 'before',
        key: 'before',
        render: (value: number) => formatNumber(value)},
      {
        title: t('debug.performance.comparison.after'),
        dataIndex: 'after',
        key: 'after',
        render: (value: number) => formatNumber(value)},
      {
        title: t('debug.performance.comparison.change'),
        dataIndex: 'change',
        key: 'change',
        render: (value: number) => formatNumber(value)},
      {
        title: t('debug.performance.comparison.percentChange'),
        dataIndex: 'percentChange',
        key: 'percentChange',
        render: (value: number, record: any) => (
          <span style={{ color: getChangeStatusColor(value, record.isImprovement) }}>
            {formatPercentChange(value)}
          </span>
        )},
    ];
    
    // 生成表格数据
    const generateTableData = (metrics: PerformanceMetricType[]) => {
      return metrics.map(type => {
        const change = metricChanges[type];
        if (!change) return null;
        
        return {
          key: type,
          metric: type,
          before: change.baseValue,
          after: change.compareValue,
          change: change.absoluteChange,
          percentChange: change.percentChange,
          isImprovement: change.isImprovement};
      }).filter(Boolean);
    };
    
    const tabItems = [
      {
        key: 'rendering',
        label: t('debug.performance.comparison.renderingMetrics'),
        children: (
          <Table
            columns={columns}
            dataSource={generateTableData(renderingMetrics)}
            pagination={false}
            size="small"
          />
        )
      },
      {
        key: 'memory',
        label: t('debug.performance.comparison.memoryMetrics'),
        children: (
          <Table
            columns={columns}
            dataSource={generateTableData(memoryMetrics)}
            pagination={false}
            size="small"
          />
        )
      },
      {
        key: 'system',
        label: t('debug.performance.comparison.systemMetrics'),
        children: (
          <Table
            columns={columns}
            dataSource={generateTableData(systemMetrics)}
            pagination={false}
            size="small"
          />
        )
      },
      {
        key: 'physics',
        label: t('debug.performance.comparison.physicsMetrics'),
        children: (
          <Table
            columns={columns}
            dataSource={generateTableData(physicsMetrics)}
            pagination={false}
            size="small"
          />
        )
      }
    ];

    if (otherMetrics.length > 0) {
      tabItems.push({
        key: 'other',
        label: t('debug.performance.comparison.otherMetrics'),
        children: (
          <Table
            columns={columns}
            dataSource={generateTableData(otherMetrics)}
            pagination={false}
            size="small"
          />
        )
      });
    }

    return (
      <div className="comparison-metrics">
        <Tabs
          defaultActiveKey="rendering"
          items={tabItems}
        />
      </div>
    );
  };
  
  // 渲染图表标签页
  const renderChartsTab = () => {
    if (!comparisonResult) {
      return (
        <Empty
          description={t('debug.performance.comparison.noComparisonData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    // 图表功能暂时不可用，需要安装 @ant-design/charts 包
    
    return (
      <div className="comparison-charts">
        <Title level={4}>{t('debug.performance.comparison.keyMetricsComparison')}</Title>
        <div className="chart-container">
          <Empty
            description={t('debug.performance.comparison.chartNotAvailable')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>

        {/* 图表功能需要安装 @ant-design/charts 包 */}
      </div>
    );
  };
  
  // 渲染快照管理标签页
  const renderSnapshotsTab = () => {
    const columns = [
      {
        title: t('debug.performance.comparison.name'),
        dataIndex: 'name',
        key: 'name'},
      {
        title: t('debug.performance.comparison.description'),
        dataIndex: 'description',
        key: 'description',
        ellipsis: true},
      {
        title: t('debug.performance.comparison.date'),
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp: number) => new Date(timestamp).toLocaleString()},
      {
        title: t('debug.performance.comparison.tags'),
        dataIndex: 'tags',
        key: 'tags',
        render: (tags: string[]) => (
          <>
            {tags && tags.map(tag => (
              <Tag key={tag}>{tag}</Tag>
            ))}
          </>
        )},
      {
        title: t('debug.performance.comparison.actions'),
        key: 'actions',
        render: (_text: string, record: PerformanceSnapshot) => (
          <Space>
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => confirmDeleteSnapshot(record.id)}
            />
          </Space>
        )},
    ];
    
    return (
      <div className="snapshots-management">
        <div className="snapshots-header">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={createSnapshot}
          >
            {t('debug.performance.comparison.createSnapshot')}
          </Button>
        </div>
        <Table
          columns={columns}
          dataSource={snapshots}
          rowKey="id"
          pagination={false}
        />
      </div>
    );
  };
  
  return (
    <div className={`performance-comparison-panel ${className || ''}`}>
      {renderSnapshotSelector()}
      
      <Divider />
      
{(() => {
        const mainTabItems = [
          {
            key: 'overview',
            label: t('debug.performance.comparison.overview'),
            children: renderOverviewTab()
          },
          {
            key: 'metrics',
            label: t('debug.performance.comparison.metrics'),
            children: renderMetricsTab()
          },
          {
            key: 'charts',
            label: t('debug.performance.comparison.charts'),
            children: renderChartsTab()
          },
          {
            key: 'snapshots',
            label: t('debug.performance.comparison.snapshots'),
            children: renderSnapshotsTab()
          }
        ];

        return (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={mainTabItems}
          />
        );
      })()}
      
      {/* 创建快照对话框 */}
      <Modal
        title={t('debug.performance.comparison.createSnapshot')}
        open={isCreatingSnapshot}
        onOk={() = destroyOnClose keyboard={true} maskClosable={true}> newSnapshotForm.submit()}
        onCancel={() => {
          try {
            setIsCreatingSnapshot(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <Form
          form={newSnapshotForm}
          layout="vertical"
          onFinish={handleSnapshotCreateSubmit}
        >
          <Form.Item
            name="name"
            label={t('debug.performance.comparison.snapshotName')}
            rules={[{ required: true, message: t('debug.performance.comparison.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label={t('debug.performance.comparison.snapshotDescription')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="tags"
            label={t('debug.performance.comparison.snapshotTags')}
            extra={t('debug.performance.comparison.tagsHint')}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 删除快照确认对话框 */}
      <Modal
        title={t('debug.performance.comparison.confirmDelete')}
        open={isDeleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setIsDeleteModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
      >
        <p>{t('debug.performance.comparison.deleteWarning')}</p>
      </Modal>
    </div>
  );
};

export default PerformanceComparisonPanel;
