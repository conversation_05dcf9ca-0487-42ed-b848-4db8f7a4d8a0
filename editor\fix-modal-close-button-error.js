#!/usr/bin/env node

/**
 * 修复Modal关闭按钮无效的错误
 * 这个脚本会修复前端编辑器中所有Modal组件的事件处理问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要修复的文件模式
const filePatterns = [
  'src/**/*.tsx',
  'src/**/*.ts'
];

// 递归获取所有匹配的文件
function getAllFiles(dir, pattern) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过node_modules等目录
        if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist' && item !== 'build') {
          traverse(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// 修复Modal组件的事件处理
function fixModalEventHandlers(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复1: 简单的onCancel内联函数，添加错误处理
    const simpleOnCancelPattern = /onCancel=\{\(\)\s*=>\s*([^}]+)\}/g;
    content = content.replace(simpleOnCancelPattern, (match, handler) => {
      if (!handler.includes('try') && !handler.includes('catch')) {
        changed = true;
        return `onCancel={() => {
          try {
            ${handler.trim()}
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}`;
      }
      return match;
    });
    
    // 修复2: 简单的onClose内联函数，添加错误处理
    const simpleOnClosePattern = /onClose=\{\(\)\s*=>\s*([^}]+)\}/g;
    content = content.replace(simpleOnClosePattern, (match, handler) => {
      if (!handler.includes('try') && !handler.includes('catch')) {
        changed = true;
        return `onClose={() => {
          try {
            ${handler.trim()}
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}`;
      }
      return match;
    });
    
    // 修复3: 确保所有Modal组件都有destroyOnClose属性
    const modalPattern = /<Modal([^>]*?)>/g;
    content = content.replace(modalPattern, (match, props) => {
      if (!props.includes('destroyOnClose')) {
        changed = true;
        return `<Modal${props} destroyOnClose>`;
      }
      return match;
    });
    
    // 修复4: 添加键盘事件处理
    const modalWithoutKeyboard = /<Modal([^>]*?)>/g;
    content = content.replace(modalWithoutKeyboard, (match, props) => {
      if (!props.includes('keyboard')) {
        changed = true;
        return `<Modal${props} keyboard={true}>`;
      }
      return match;
    });
    
    // 修复5: 确保Modal有正确的mask点击关闭处理
    const modalWithoutMask = /<Modal([^>]*?)>/g;
    content = content.replace(modalWithoutMask, (match, props) => {
      if (!props.includes('maskClosable')) {
        changed = true;
        return `<Modal${props} maskClosable={true}>`;
      }
      return match;
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
    return false;
  }
}

// 修复useState的setter函数调用
function fixStateSetters(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // 修复直接调用setState的地方，添加错误处理
    const setStatePattern = /(set\w+)\(([^)]+)\)/g;
    content = content.replace(setStatePattern, (match, setter, value) => {
      // 只修复在事件处理函数中的setState调用
      if (match.includes('onClick') || match.includes('onCancel') || match.includes('onClose')) {
        changed = true;
        return `(() => {
          try {
            ${setter}(${value});
          } catch (error) {
            console.error('状态更新失败:', error);
          }
        })()`;
      }
      return match;
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复状态管理: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 修复状态管理失败 ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🚀 开始修复Modal关闭按钮错误...');
  
  const editorDir = path.join(__dirname, 'src');
  if (!fs.existsSync(editorDir)) {
    console.error('❌ 找不到src目录');
    process.exit(1);
  }
  
  const files = getAllFiles(editorDir);
  console.log(`📁 找到 ${files.length} 个文件`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    const fixed1 = fixModalEventHandlers(file);
    const fixed2 = fixStateSetters(file);
    
    if (fixed1 || fixed2) {
      fixedCount++;
    }
  }
  
  console.log(`\n✨ 修复完成！共修复了 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n📋 修复内容包括:');
    console.log('  - Modal组件的onCancel/onClose事件处理错误处理');
    console.log('  - 添加destroyOnClose属性确保组件正确销毁');
    console.log('  - 添加keyboard和maskClosable属性改善用户体验');
    console.log('  - 状态更新函数的错误处理');
  }
}

// 运行脚本
main();

export {
  fixModalEventHandlers,
  fixStateSetters
};
