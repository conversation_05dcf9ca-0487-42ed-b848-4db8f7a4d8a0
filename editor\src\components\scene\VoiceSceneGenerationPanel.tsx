/**
 * 语音场景生成面板
 * 提供语音输入的场景生成功能
 */
import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import {
  Card,
  Button,
  Space,
  Progress,
  Typography,
  Alert,
  Tooltip,
  Divider,
  Tag,
  List,
  Avatar
} from 'antd';
import {
  AudioOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  StopOutlined,
  SoundOutlined,
  LoadingOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

/**
 * 语音波形组件
 */
const VoiceWaveform: React.FC<{ isActive: boolean; amplitude?: number }> = ({ 
  isActive, 
  amplitude = 0.5 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      if (isActive) {
        const centerY = canvas.height / 2;
        const barCount = 20;
        const barWidth = canvas.width / barCount;
        
        for (let i = 0; i < barCount; i++) {
          const height = Math.random() * amplitude * canvas.height * 0.8;
          const x = i * barWidth;
          const y = centerY - height / 2;
          
          ctx.fillStyle = '#1890ff';
          ctx.fillRect(x + 2, y, barWidth - 4, height);
        }
      } else {
        // 静态波形
        const centerY = canvas.height / 2;
        ctx.strokeStyle = '#d9d9d9';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(0, centerY);
        ctx.lineTo(canvas.width, centerY);
        ctx.stroke();
      }
      
      if (isActive) {
        animationRef.current = requestAnimationFrame(draw);
      }
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, amplitude]);

  return (
    <canvas
      ref={canvasRef}
      width={300}
      height={80}
      style={{
        border: '1px solid #d9d9d9',
        borderRadius: '4px',
        backgroundColor: '#fafafa'
      }}
    />
  );
};

/**
 * 语音场景生成面板Ref接口
 */
export interface VoiceSceneGenerationPanelRef {
  /** 使用指定描述重新生成 */
  regenerateWithDescription: (description: string) => void;
  /** 获取当前转录文本 */
  getCurrentTranscript: () => string;
  /** 设置转录文本 */
  setTranscript: (transcript: string) => void;
  /** 开始语音识别 */
  startListening: () => void;
  /** 停止语音识别 */
  stopListening: () => void;
}

/**
 * 语音场景生成面板属性
 */
interface VoiceSceneGenerationPanelProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 生成完成回调 */
  onSceneGenerated?: (scene: any) => void;
  /** 错误回调 */
  onError?: (error: string) => void;
}

/**
 * 语音场景生成面板
 */
const VoiceSceneGenerationPanel = forwardRef<VoiceSceneGenerationPanelRef, VoiceSceneGenerationPanelProps>(({
  disabled = false,
  onSceneGenerated,
  onError
}, ref) => {
  
  // 状态管理
  const [isListening, setIsListening] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [suggestions] = useState<string[]>([
    '创建一个现代办公室',
    '设计一个温馨的客厅',
    '构建一个简约的卧室',
    '生成一个开放式厨房'
  ]);
  const [interactionHistory, setInteractionHistory] = useState<any[]>([]);

  // 语音控制器引用
  const voiceControllerRef = useRef<any>(null);

  /**
   * 初始化语音控制器
   */
  useEffect(() => {
    const initializeVoiceController = async () => {
      try {
        // 这里应该初始化实际的语音控制器
        // const { VoiceSceneGenerationController } = await import('../../engine/ai/scene');
        // voiceControllerRef.current = new VoiceSceneGenerationController();
        
        // 模拟初始化
        voiceControllerRef.current = {
          startVoiceSession: async () => ({ sessionId: 'mock_session', status: 'active' }),
          stopVoiceSession: async () => {},
          pauseListening: async () => {},
          resumeListening: async () => {},
          getSessionState: () => ({
            isListening: false,
            isGenerating: false,
            isSpeaking: false,
            currentTranscript: '',
            generationProgress: 0
          })
        };
      } catch (error) {
        console.error('初始化语音控制器失败:', error);
        setError('语音功能初始化失败');
      }
    };

    initializeVoiceController();

    return () => {
      // 清理资源
      if (voiceControllerRef.current) {
        voiceControllerRef.current.destroy?.();
      }
    };
  }, []);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    regenerateWithDescription: (description: string) => {
      setCurrentTranscript(description);
      // 自动触发生成
      setTimeout(() => {
        handleVoiceInput(description);
      }, 100);
    },
    getCurrentTranscript: () => currentTranscript,
    setTranscript: (transcript: string) => {
      setCurrentTranscript(transcript);
    },
    startListening: () => {
      if (!isListening) {
        toggleListening();
      }
    },
    stopListening: () => {
      if (isListening) {
        toggleListening();
      }
    }
  }), [currentTranscript, isListening]);

  /**
   * 开始/停止语音识别
   */
  const toggleListening = async () => {
    if (!voiceControllerRef.current) {
      setError('语音控制器未初始化');
      return;
    }

    try {
      if (!isListening) {
        // 开始语音会话
        const result = await voiceControllerRef.current.startVoiceSession();
        if (result.status === 'active') {
          setSessionId(result.sessionId);
          setIsListening(true);
          setError(null);
        } else {
          setError(result.error || '启动语音会话失败');
        }
      } else {
        // 停止语音会话
        if (sessionId) {
          await voiceControllerRef.current.stopVoiceSession(sessionId);
        }
        setIsListening(false);
        setSessionId(null);
        setCurrentTranscript('');
      }
    } catch (error) {
      console.error('语音控制失败:', error);
      setError(error instanceof Error ? error.message : '语音控制失败');
    }
  };

  /**
   * 暂停/恢复语音识别
   */
  const togglePause = async () => {
    if (!voiceControllerRef.current || !sessionId) return;

    try {
      if (isListening) {
        await voiceControllerRef.current.pauseListening(sessionId);
        setIsListening(false);
      } else {
        await voiceControllerRef.current.resumeListening(sessionId);
        setIsListening(true);
      }
    } catch (error) {
      console.error('暂停/恢复失败:', error);
      setError(error instanceof Error ? error.message : '操作失败');
    }
  };

  /**
   * 执行快速命令
   */
  const executeQuickCommand = (command: string) => {
    setCurrentTranscript(command);
    // 模拟语音输入
    setTimeout(() => {
      handleVoiceInput(command);
    }, 100);
  };

  /**
   * 处理语音输入
   */
  const handleVoiceInput = async (transcript: string) => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 模拟场景生成过程
      const steps = [
        { progress: 20, message: '理解场景描述...' },
        { progress: 40, message: '生成场景布局...' },
        { progress: 60, message: '匹配场景资产...' },
        { progress: 80, message: '构建3D场景...' },
        { progress: 100, message: '场景生成完成！' }
      ];

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800));
        setGenerationProgress(step.progress);
      }

      // 模拟生成结果
      const mockScene = {
        id: 'generated_scene_' + Date.now(),
        name: '生成的场景',
        description: transcript,
        elements: ['桌子', '椅子', '灯'],
        confidence: 0.85
      };

      // 添加到历史记录
      const interaction = {
        timestamp: Date.now(),
        userInput: transcript,
        systemResponse: '场景已成功生成',
        confidence: 0.85
      };
      setInteractionHistory(prev => [...prev, interaction]);

      // 回调
      onSceneGenerated?.(mockScene);

      // 模拟系统语音反馈
      setIsSpeaking(true);
      setTimeout(() => {
        setIsSpeaking(false);
      }, 2000); // 2秒后停止说话状态

      // 重置状态
      setIsGenerating(false);
      setGenerationProgress(0);
      setCurrentTranscript('');

    } catch (error) {
      console.error('场景生成失败:', error);
      setError(error instanceof Error ? error.message : '场景生成失败');
      setIsGenerating(false);
      setGenerationProgress(0);
      onError?.(error instanceof Error ? error.message : '场景生成失败');
    }
  };

  /**
   * 渲染语音输入区域
   */
  const renderVoiceInputSection = () => (
    <div className="voice-input-section" style={{ textAlign: 'center', padding: '20px 0' }}>
      <div style={{ marginBottom: '20px' }}>
        <VoiceWaveform isActive={isListening} />
      </div>

      <Space size="large">
        <Tooltip title={isListening ? '停止录音' : '开始录音'}>
          <Button
            type="primary"
            size="large"
            shape="circle"
            icon={isListening ? <StopOutlined /> : <AudioOutlined />}
            onClick={toggleListening}
            disabled={disabled || isGenerating}
            className={isListening ? 'listening-button' : ''}
            style={{
              width: '60px',
              height: '60px',
              fontSize: '24px',
              backgroundColor: isListening ? '#ff4d4f' : '#1890ff'
            }}
          />
        </Tooltip>

        {sessionId && (
          <Tooltip title={isListening ? '暂停' : '继续'}>
            <Button
              size="large"
              shape="circle"
              icon={isListening ? <PauseOutlined /> : <PlayCircleOutlined />}
              onClick={togglePause}
              disabled={disabled || isGenerating}
            />
          </Tooltip>
        )}

        {isSpeaking && (
          <Tooltip title="系统正在说话">
            <Button
              size="large"
              shape="circle"
              icon={<SoundOutlined />}
              disabled
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
        )}
      </Space>

      <div style={{ marginTop: '20px', minHeight: '60px' }}>
        {currentTranscript ? (
          <div>
            <Text strong>识别结果：</Text>
            <Paragraph style={{ 
              fontSize: '16px', 
              padding: '10px', 
              backgroundColor: '#f6f6f6', 
              borderRadius: '4px',
              margin: '10px 0'
            }}>
              {currentTranscript}
            </Paragraph>
          </div>
        ) : (
          <Text type="secondary">
            {isListening ? '正在监听，请说话...' : '点击麦克风按钮开始语音描述场景'}
          </Text>
        )}
      </div>
    </div>
  );

  /**
   * 渲染生成进度
   */
  const renderGenerationProgress = () => {
    if (!isGenerating && generationProgress === 0) return null;

    return (
      <div style={{ margin: '20px 0' }}>
        <Progress
          percent={generationProgress}
          status={isGenerating ? 'active' : 'success'}
          format={(percent) => `${percent}%`}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        {isGenerating && (
          <div style={{ textAlign: 'center', marginTop: '10px' }}>
            <LoadingOutlined style={{ marginRight: '8px' }} />
            <Text>正在生成场景...</Text>
          </div>
        )}
      </div>
    );
  };

  /**
   * 渲染快速命令
   */
  const renderQuickCommands = () => (
    <div style={{ margin: '20px 0' }}>
      <Title level={5}>快速命令</Title>
      <Space wrap>
        {suggestions.map((suggestion, index) => (
          <Button
            key={index}
            size="small"
            onClick={() => executeQuickCommand(suggestion)}
            disabled={disabled || isGenerating}
          >
            {suggestion.substring(0, 8)}...
          </Button>
        ))}
      </Space>
    </div>
  );

  /**
   * 渲染交互历史
   */
  const renderInteractionHistory = () => {
    if (interactionHistory.length === 0) return null;

    return (
      <div style={{ margin: '20px 0' }}>
        <Title level={5}>对话历史</Title>
        <List
          size="small"
          dataSource={interactionHistory.slice(-3)} // 只显示最近3条
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar icon={<AudioOutlined />} size="small" />}
                title={
                  <div>
                    <Text strong>用户：</Text>
                    <Text>{item.userInput}</Text>
                  </div>
                }
                description={
                  <div>
                    <Text type="secondary">系统：{item.systemResponse}</Text>
                    <Tag color="blue" style={{ marginLeft: '8px' }}>
                      置信度: {Math.round(item.confidence * 100)}%
                    </Tag>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>
    );
  };

  return (
    <Card 
      title="语音场景生成" 
      className="voice-scene-panel"
      extra={
        sessionId && (
          <Tag color="green">
            会话活跃
          </Tag>
        )
      }
    >
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          closable
          onClose={() => {
          try {
            setError(null)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
          style={{ marginBottom: '16px' }}
        />
      )}

      {renderVoiceInputSection()}
      
      {renderGenerationProgress()}

      <Divider />

      {renderQuickCommands()}

      {renderInteractionHistory()}

      <style>{`
        .listening-button {
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
          }
        }
      `}</style>
    </Card>
  );
});

// 设置组件显示名称
VoiceSceneGenerationPanel.displayName = 'VoiceSceneGenerationPanel';

export default VoiceSceneGenerationPanel;
