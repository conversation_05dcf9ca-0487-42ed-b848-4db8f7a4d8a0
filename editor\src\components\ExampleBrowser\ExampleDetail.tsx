/**
 * 示例项目详情组件
 * 用于显示示例项目的详细信息和操作选项
 */
import React, { useState } from 'react';
import { Button, Tabs, Tag, Carousel, Descriptions, Card, List, Tooltip, Modal } from 'antd';
import {
  ArrowLeftOutlined,
  ImportOutlined,
  EyeOutlined,
  StarOutlined,
  StarFilled,
  DownloadOutlined,
  ShareAltOutlined,
  UserOutlined,
  FileOutlined,
  PlayCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import MarkdownViewer from '../common/MarkdownViewer';
import { Example } from '../../types/example';
import { getCategoryColor, getTagColor } from '../../utils/exampleUtils';
import './ExampleDetail.less';

const { TabPane } = Tabs;

interface ExampleDetailProps {
  example: Example;
  onBack: () => void;
  onImport: () => void;
  onFavoriteToggle?: (favorited: boolean) => void;
}

/**
 * 示例项目详情组件
 */
const ExampleDetail: React.FC<ExampleDetailProps> = ({ example, onBack, onImport, onFavoriteToggle }) => {
  const { t } = useTranslation();
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [favorited, setFavorited] = useState<boolean>(example.favorited || false);

  /**
   * 格式化日期
   * @param dateString 日期字符串
   * @returns 格式化后的日期字符串
   */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  /**
   * 处理收藏点击
   */
  const handleFavoriteClick = () => {
    const newFavorited = !favorited;
    setFavorited(newFavorited);
    if (onFavoriteToggle) {
      onFavoriteToggle(newFavorited);
    }
  };

  /**
   * 处理预览点击
   */
  const handlePreviewClick = () => {
    setPreviewVisible(true);
  };

  /**
   * 处理分享点击
   */
  const handleShareClick = () => {
    // TODO: 实现分享功能
    console.log('分享示例项目:', example.id);
  };

  /**
   * 处理下载点击
   */
  const handleDownloadClick = () => {
    // TODO: 实现下载功能
    console.log('下载示例项目:', example.id);
  };

  /**
   * 获取难度标签
   * @param difficulty 难度级别
   * @returns 难度标签元素
   */
  const getDifficultyTag = (difficulty: string): JSX.Element => {
    let color = '';
    let text = '';

    switch (difficulty) {
      case 'beginner':
        color = 'green';
        text = t('exampleBrowser.difficultyBeginner') as string;
        break;
      case 'intermediate':
        color = 'blue';
        text = t('exampleBrowser.difficultyIntermediate') as string;
        break;
      case 'advanced':
        color = 'orange';
        text = t('exampleBrowser.difficultyAdvanced') as string;
        break;
      case 'expert':
        color = 'red';
        text = t('exampleBrowser.difficultyExpert') as string;
        break;
      default:
        color = 'default';
        text = difficulty;
    }

    return (
      <Tag color={color} className="difficulty-tag">
        {text}
      </Tag>
    );
  };

  return (
    <div className="example-detail">
      <div className="detail-header">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          className="back-button"
        >
          {t('exampleBrowser.back')}
        </Button>
        <div className="header-actions">
          <Tooltip title={favorited ? t('exampleBrowser.unfavorite') as string : t('exampleBrowser.favorite') as string}>
            <Button 
              icon={favorited ? <StarFilled /> : <StarOutlined />} 
              onClick={handleFavoriteClick}
              type={favorited ? 'primary' : 'default'}
              className="action-button"
            />
          </Tooltip>
          <Tooltip title={t('exampleBrowser.preview') as string}>
            <Button 
              icon={<EyeOutlined />} 
              onClick={handlePreviewClick}
              className="action-button"
            />
          </Tooltip>
          <Tooltip title={t('exampleBrowser.share') as string}>
            <Button 
              icon={<ShareAltOutlined />} 
              onClick={handleShareClick}
              className="action-button"
            />
          </Tooltip>
          <Tooltip title={t('exampleBrowser.download') as string}>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleDownloadClick}
              className="action-button"
            />
          </Tooltip>
          <Button 
            type="primary" 
            icon={<ImportOutlined />} 
            onClick={onImport}
            className="import-button"
          >
            {t('exampleBrowser.import')}
          </Button>
        </div>
      </div>

      <div className="detail-content">
        <div className="detail-main">
          <div className="detail-gallery">
            <Carousel autoplay>
              {example.images.map((image, index) => (
                <div key={index} className="gallery-item">
                  <img src={image} alt={`${example.title} - ${index + 1}`} />
                </div>
              ))}
            </Carousel>
          </div>

          <div className="detail-info">
            <h1 className="detail-title">{example.title}</h1>
            <div className="detail-meta">
              <Tag color={getCategoryColor(example.category)}>
                {t(`exampleBrowser.category.${example.category}`)}
              </Tag>
              {example.difficulty && getDifficultyTag(example.difficulty)}
              <span className="detail-popularity">
                <StarOutlined /> {example.popularity}
              </span>
              <span className="detail-date">
                <CalendarOutlined /> {formatDate(example.createdAt)}
              </span>
              <span className="detail-author">
                <UserOutlined /> {example.author}
              </span>
            </div>
            <div className="detail-tags">
              {example.tags.map((tag, index) => (
                <Tag key={index} color={getTagColor(tag)}>
                  {tag}
                </Tag>
              ))}
            </div>
            <p className="detail-description">{example.description}</p>
          </div>

          <Tabs defaultActiveKey="overview" className="detail-tabs">
            <TabPane tab={t('exampleBrowser.overview') as string} key="overview">
              <div className="tab-content">
                <MarkdownViewer content={example.content || ''} />
              </div>
            </TabPane>
            <TabPane tab={t('exampleBrowser.features') as string} key="features">
              <div className="tab-content">
                <List
                  itemLayout="horizontal"
                  dataSource={example.features || []}
                  renderItem={feature => (
                    <List.Item>
                      <List.Item.Meta
                        title={feature.title}
                        description={feature.description}
                      />
                    </List.Item>
                  )}
                />
              </div>
            </TabPane>
            <TabPane tab={t('exampleBrowser.files') as string} key="files">
              <div className="tab-content">
                <List
                  itemLayout="horizontal"
                  dataSource={example.files || []}
                  renderItem={file => (
                    <List.Item
                      actions={[
                        <Button icon={<EyeOutlined />} size="small" key="view" />,
                        <Button icon={<DownloadOutlined />} size="small" key="download" />
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<FileOutlined />}
                        title={file.name}
                        description={`${file.size} - ${formatDate(file.updatedAt)}`}
                      />
                    </List.Item>
                  )}
                />
              </div>
            </TabPane>
            <TabPane tab={t('exampleBrowser.tutorials') as string} key="tutorials">
              <div className="tab-content">
                <List
                  itemLayout="horizontal"
                  dataSource={example.tutorials || []}
                  renderItem={tutorial => (
                    <List.Item
                      actions={[
                        <Button icon={<PlayCircleOutlined />} size="small" key="play" />
                      ]}
                    >
                      <List.Item.Meta
                        title={tutorial.title}
                        description={tutorial.description}
                      />
                    </List.Item>
                  )}
                />
              </div>
            </TabPane>
          </Tabs>
        </div>

        <div className="detail-sidebar">
          <Card title={t('exampleBrowser.requirements') as string} className="sidebar-card">
            <Descriptions column={1} size="small">
              <Descriptions.Item label={t('exampleBrowser.engineVersion') as string}>
                {example.requirements?.engineVersion || '1.0.0+'}
              </Descriptions.Item>
              <Descriptions.Item label={t('exampleBrowser.editorVersion') as string}>
                {example.requirements?.editorVersion || '1.0.0+'}
              </Descriptions.Item>
              <Descriptions.Item label={t('exampleBrowser.dependencies') as string}>
                {example.requirements?.dependencies?.join(', ') || t('exampleBrowser.none') as string}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title={t('exampleBrowser.relatedExamples') as string} className="sidebar-card">
            <List
              itemLayout="horizontal"
              dataSource={example.relatedExamples || []}
              renderItem={related => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<img src={related.previewImage} alt={related.title} className="related-image" />}
                    title={related.title}
                    description={related.description}
                  />
                </List.Item>
              )}
            />
          </Card>
        </div>
      </div>

      <Modal
        title={example.title}
        open={previewVisible}
        onCancel={() = destroyOnClose keyboard={true} maskClosable={true}> {
          try {
            setPreviewVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        footer={null}
        width="80%"
        className="preview-modal"
      >
        <iframe
          src={example.previewUrl}
          title={example.title}
          className="preview-iframe"
        />
      </Modal>
    </div>
  );
};

export default ExampleDetail;
