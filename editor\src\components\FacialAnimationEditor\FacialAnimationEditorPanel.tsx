/**
 * 面部动画编辑器面板
 * 提供面部动画编辑的主界面
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Tabs, Input, Select, Space, message, Modal } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  SaveOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import TimelineEditor from './TimelineEditor';
import KeyframeEditor from './KeyframeEditor';
import CurveEditor from './CurveEditor';
import FacialAnimationPreview from './FacialAnimationPreview';
import AIFacialAnimationGenerator from './AIFacialAnimationGenerator';
import { AIFacialAnimationService } from '../../services/AIFacialAnimationService';
import './FacialAnimationEditorPanel.less';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 面部动画编辑器面板属性
 */
interface FacialAnimationEditorPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 初始动画片段名称 */
  initialClipName?: string;
}

/**
 * 面部动画编辑器面板
 */
const FacialAnimationEditorPanel: React.FC<FacialAnimationEditorPanelProps> = ({
  entityId,
  initialClipName
}) => {
  const { t } = useTranslation();

  // 状态
  const [clipName, setClipName] = useState<string>(initialClipName || '新建动画');
  const [duration] = useState<number>(5.0);
  const [frameRate] = useState<number>(30);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isLooping] = useState<boolean>(true);
  const [availableClips, setAvailableClips] = useState<string[]>([]);
  const [selectedTab, setSelectedTab] = useState<string>('timeline');

  // AI面部动画生成相关状态
  const [showAIGenerator, setShowAIGenerator] = useState<boolean>(false);
  const [, setAiGenerating] = useState<boolean>(false);

  // 引用
  const animationTimerRef = useRef<number | null>(null);
  const lastUpdateTimeRef = useRef<number>(0);
  const aiServiceRef = useRef<AIFacialAnimationService>(AIFacialAnimationService.getInstance());

  // 加载动画片段
  useEffect(() => {
    if (entityId) {
      // 这里应该从引擎中加载实体的动画片段
      // 示例代码，实际实现需要与引擎集成
      setAvailableClips(['默认表情', '说话', '微笑', '惊讶']);
    }
  }, [entityId]);

  // 播放/暂停动画
  useEffect(() => {
    if (isPlaying) {
      lastUpdateTimeRef.current = performance.now();
      animationTimerRef.current = window.requestAnimationFrame(updateAnimation);
    } else if (animationTimerRef.current) {
      window.cancelAnimationFrame(animationTimerRef.current);
      animationTimerRef.current = null;
    }

    return () => {
      if (animationTimerRef.current) {
        window.cancelAnimationFrame(animationTimerRef.current);
        animationTimerRef.current = null;
      }
    };
  }, [isPlaying]);

  // 更新动画
  const updateAnimation = () => {
    const now = performance.now();
    const deltaTime = (now - lastUpdateTimeRef.current) / 1000;
    lastUpdateTimeRef.current = now;

    let newTime = currentTime + deltaTime;

    // 处理循环
    if (newTime >= duration) {
      if (isLooping) {
        newTime %= duration;
      } else {
        newTime = duration;
        setIsPlaying(false);
      }
    }

    setCurrentTime(newTime);

    if (isPlaying) {
      animationTimerRef.current = window.requestAnimationFrame(updateAnimation);
    }
  };

  // 播放/暂停
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // 跳转到开始
  const handleJumpToStart = () => {
    setCurrentTime(0);
  };

  // 跳转到结束
  const handleJumpToEnd = () => {
    setCurrentTime(duration);
  };

  // 保存动画
  const handleSaveAnimation = () => {
    // 这里应该保存动画到引擎
    // 示例代码，实际实现需要与引擎集成
    message.success(t('editor.animation.saveSuccess'));
  };

  // 导入动画
  const handleImportAnimation = () => {
    // 这里应该打开文件选择器并导入动画
    // 示例代码，实际实现需要与引擎集成
    message.info(t('editor.animation.importStarted'));
  };

  // 导出动画
  const handleExportAnimation = () => {
    // 这里应该导出动画到文件
    // 示例代码，实际实现需要与引擎集成
    message.success(t('editor.animation.exportSuccess'));
  };

  // 创建新动画
  const handleCreateNewAnimation = () => {
    // 这里应该创建新动画
    // 示例代码，实际实现需要与引擎集成
    const newClipName = `新建动画_${availableClips.length + 1}`;
    setAvailableClips([...availableClips, newClipName]);
    setClipName(newClipName);
    setCurrentTime(0);
    message.success(t('editor.animation.createSuccess'));
  };

  // 打开AI面部动画生成器
  const handleOpenAIGenerator = () => {
    setShowAIGenerator(true);
  };

  // 关闭AI面部动画生成器
  const handleCloseAIGenerator = () => {
    setShowAIGenerator(false);
  };

  // 生成AI面部动画
  const handleGenerateAIAnimation = async (request: any) => {
    if (!entityId) {
      message.error(t('editor.ai.noEntitySelected'));
      return false;
    }

    setAiGenerating(true);

    try {
      // 调用AI面部动画服务生成动画
      const success = await aiServiceRef.current.generateFacialAnimation(entityId, request);

      if (success) {
        message.success(t('editor.ai.generationStarted'));
      }

      return success;
    } catch (error) {
      console.error('生成AI面部动画失败:', error);
      message.error(t('editor.ai.generationError'));
      return false;
    } finally {
      setAiGenerating(false);
    }
  };

  // 应用AI生成的动画
  const handleApplyAIAnimation = async (animationId: string) => {
    if (!entityId) {
      message.error(t('editor.ai.noEntitySelected'));
      return;
    }

    try {
      // 调用AI面部动画服务应用动画
      const success = await aiServiceRef.current.applyGeneratedAnimation(entityId, animationId);

      if (success) {
        message.success(t('editor.ai.animationApplied'));
        // 刷新可用动画列表
        // 这里应该从引擎中重新加载实体的动画片段
      }
    } catch (error) {
      console.error('应用AI面部动画失败:', error);
      message.error(t('editor.ai.applyError'));
    }
  };

  // 预览AI生成的动画
  const handlePreviewAIAnimation = async (animationId: string) => {
    if (!entityId) {
      message.error(t('editor.ai.noEntitySelected'));
      return;
    }

    try {
      // 调用AI面部动画服务预览动画
      const success = await aiServiceRef.current.previewGeneratedAnimation(entityId, animationId);

      if (success) {
        // 预览成功，可以更新UI状态
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('预览AI面部动画失败:', error);
      message.error(t('editor.ai.previewError'));
    }
  };

  return (
    <div className="facial-animation-editor">
      <div className="facial-animation-editor-header">
        <div className="clip-info">
          <Input
            value={clipName}
            onChange={(e) => setClipName(e.target.value)}
            style={{ width: 200 }}
            placeholder={t('editor.animation.clipName') || ''}
          />
          <Select
            value={clipName}
            onChange={setClipName}
            style={{ width: 200, marginLeft: 8 }}
            placeholder={t('editor.animation.selectClip')}
          >
            {availableClips.map((clip) => (
              <Option key={clip} value={clip}>{clip}</Option>
            ))}
          </Select>
          <Button
            type="text"
            icon={<PlusOutlined />}
            onClick={handleCreateNewAnimation}
            title={t('editor.animation.createNew') || ''}
          />
        </div>

        <div className="editor-actions">
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveAnimation}
            >
              {t('editor.animation.save')}
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={handleImportAnimation}
            >
              {t('editor.animation.import')}
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExportAnimation}
            >
              {t('editor.animation.export')}
            </Button>
            <Button
              icon={<RobotOutlined />}
              onClick={handleOpenAIGenerator}
              title={t('editor.ai.generateWithAI') || ''}
            >
              {t('editor.ai.generateWithAI')}
            </Button>
          </Space>
        </div>
      </div>

      <div className="facial-animation-editor-content">
        <div className="editor-main">
          <div className="preview-panel">
            <FacialAnimationPreview
              entityId={entityId}
              currentTime={currentTime}
              isPlaying={isPlaying}
            />

            <div className="playback-controls">
              <Button icon={<StepBackwardOutlined />} onClick={handleJumpToStart} />
              <Button
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={handlePlayPause}
              />
              <Button icon={<StepForwardOutlined />} onClick={handleJumpToEnd} />
              <span className="time-display">
                {currentTime.toFixed(2)}s / {duration.toFixed(2)}s
              </span>
            </div>
          </div>

          <div className="editor-tabs">
            <Tabs activeKey={selectedTab} onChange={setSelectedTab}>
              <TabPane tab={t('editor.animation.timeline')} key="timeline">
                <TimelineEditor
                  entityId={entityId}
                  currentTime={currentTime}
                  duration={duration}
                  frameRate={frameRate}
                  onTimeChange={setCurrentTime}
                />
              </TabPane>
              <TabPane tab={t('editor.animation.keyframes')} key="keyframes">
                <KeyframeEditor
                  entityId={entityId}
                  currentTime={currentTime}
                  duration={duration}
                />
              </TabPane>
              <TabPane tab={t('editor.animation.curves')} key="curves">
                <CurveEditor
                  entityId={entityId}
                  currentTime={currentTime}
                  duration={duration}
                />
              </TabPane>
            </Tabs>
          </div>
        </div>
      </div>

      {/* AI面部动画生成对话框 */}
      <Modal
        title={t('editor.ai.facialAnimationGenerator')}
        open={showAIGenerator}
        onCancel={handleCloseAIGenerator}
        footer={null}
        width={1000}

        maskClosable={false}
        className="ai-generator-modal"
       destroyOnClose keyboard={true}>
        <AIFacialAnimationGenerator
          entityId={entityId}
          onGenerate={handleGenerateAIAnimation}
          onApply={handleApplyAIAnimation}
          onPreview={handlePreviewAIAnimation}
          onClose={handleCloseAIGenerator}
        />
      </Modal>
    </div>
  );
};

export default FacialAnimationEditorPanel;
