/**
 * 动画系统反馈表单组件
 * 专门用于收集动画系统相关的反馈
 */
import React, { useState } from 'react';
import { Form, Input, Button, Rate, Select, Radio, Checkbox, Upload, Modal, message, Collapse, Divider, Tooltip } from 'antd';
import { UploadOutlined, SendOutlined, CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService } from '../../services/FeedbackService';
import './FeedbackForm.less';

const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

export interface AnimationFeedbackFormProps {
  /** 动画子系统类型 */
  subType: 'blend' | 'stateMachine' | 'timeline' | 'retargeting' | 'general';
  /** 相关实体ID */
  entityId?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 性能数据 */
  performanceData?: any;
  /** 关闭回调 */
  onClose?: () => void;
  /** 提交成功回调 */
  onSuccess?: () => void;
}

/**
 * 动画系统反馈表单组件
 */
const AnimationFeedbackForm: React.FC<AnimationFeedbackFormProps> = ({
  subType,
  entityId,
  resourceId,
  performanceData,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [includePerformanceData, setIncludePerformanceData] = useState(true);
  const [feedbackType, setFeedbackType] = useState('improvement');

  // 动画系统特定的反馈类型选项
  const animationFeedbackTypeOptions = [
    { label: t('feedback.animation.type.bug') || '', value: 'bug' },
    { label: t('feedback.animation.type.feature') || '', value: 'feature' },
    { label: t('feedback.animation.type.improvement') || '', value: 'improvement' },
    { label: t('feedback.animation.type.performance') || '', value: 'performance' },
    { label: t('feedback.animation.type.usability') || '', value: 'usability' },
    { label: t('feedback.animation.type.other') || '', value: 'other' }
  ];

  // 动画混合系统特定的问题类别
  const blendSystemIssueOptions = [
    { label: t('feedback.animation.blend.issue.weight') || '', value: 'weight' },
    { label: t('feedback.animation.blend.issue.mask') || '', value: 'mask' },
    { label: t('feedback.animation.blend.issue.transition') || '', value: 'transition' },
    { label: t('feedback.animation.blend.issue.performance') || '', value: 'performance' },
    { label: t('feedback.animation.blend.issue.ui') || '', value: 'ui' },
    { label: t('feedback.animation.blend.issue.other') || '', value: 'other' }
  ];

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      // 准备反馈数据
      const feedbackData = {
        ...values,
        type: 'animation',
        subType,
        entityId,
        resourceId,
        timestamp: new Date().toISOString(),
        browser: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        performanceData: includePerformanceData ? performanceData : null
      };
      
      // 提交反馈
      await FeedbackService.submitFeedback(feedbackData);
      
      // 重置表单
      form.resetFields();
      
      // 显示成功提示
      setShowSuccessModal(true);
      
      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error(t('feedback.submitError') || '提交反馈失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭成功提示
  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    if (onClose) {
      onClose();
    }
  };

  // 获取子系统标题
  const getSubTypeTitle = () => {
    switch (subType) {
      case 'blend':
        return t('feedback.animation.subType.blend');
      case 'stateMachine':
        return t('feedback.animation.subType.stateMachine');
      case 'timeline':
        return t('feedback.animation.subType.timeline');
      case 'retargeting':
        return t('feedback.animation.subType.retargeting');
      default:
        return t('feedback.animation.subType.general');
    }
  };

  // 处理反馈类型变更
  const handleFeedbackTypeChange = (e: any) => {
    setFeedbackType(e.target.value);
  };

  return (
    <div className="feedback-form animation-feedback-form">
      <div className="feedback-form-header">
        <h2>{t('feedback.title.animation')}</h2>
        <div className="feedback-subtype">{getSubTypeTitle()}</div>
        {onClose && (
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            className="close-button"
          />
        )}
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          feedbackType: 'improvement',
          satisfaction: 3,
          allowContact: true,
          includePerformanceData: true
        }}
      >
        {/* 反馈类型 */}
        <Form.Item
          name="feedbackType"
          label={t('feedback.form.feedbackType') || ''}
          rules={[{ required: true, message: t('feedback.form.required') || '' }]}
        >
          <Radio.Group
            options={animationFeedbackTypeOptions}
            onChange={handleFeedbackTypeChange}
          />
        </Form.Item>

        {/* 动画混合系统特定问题类别 */}
        {subType === 'blend' && (
          <Form.Item
            name="issueCategory"
            label={t('feedback.animation.blend.issueCategory') || ''}
            rules={[{ required: feedbackType === 'bug' || feedbackType === 'improvement', message: t('feedback.form.required') || '' }]}
          >
            <Select placeholder={t('feedback.animation.blend.selectIssueCategory') || ''}>
              {blendSystemIssueOptions.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* 标题 */}
        <Form.Item
          name="title"
          label={t('feedback.form.title') || ''}
          rules={[{ required: true, message: t('feedback.form.required') || '' }]}
        >
          <Input placeholder={t('feedback.form.titlePlaceholder') || ''} maxLength={100} />
        </Form.Item>

        {/* 描述 */}
        <Form.Item
          name="description"
          label={t('feedback.form.description') || ''}
          rules={[{ required: true, message: t('feedback.form.required') || '' }]}
        >
          <TextArea
            placeholder={t('feedback.animation.descriptionPlaceholder') || ''}
            autoSize={{ minRows: 4, maxRows: 8 }}
            maxLength={2000}
          />
        </Form.Item>

        {/* 重现步骤 - 仅在报告bug时显示 */}
        {feedbackType === 'bug' && (
          <Form.Item
            name="reproductionSteps"
            label={t('feedback.form.reproductionSteps') || ''}
            rules={[{ required: true, message: t('feedback.form.required') || '' }]}
          >
            <TextArea
              placeholder={t('feedback.animation.reproductionStepsPlaceholder') || ''}
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
        )}

        {/* 满意度评分 */}
        <Form.Item
          name="satisfaction"
          label={t('feedback.form.satisfaction') || ''}
        >
          <Rate allowHalf />
        </Form.Item>

        {/* 改进建议 - 仅在提出改进或功能请求时显示 */}
        {(feedbackType === 'improvement' || feedbackType === 'feature' || feedbackType === 'usability') && (
          <Form.Item
            name="suggestions"
            label={t('feedback.form.suggestions') || ''}
            rules={[{ required: true, message: t('feedback.form.required') || '' }]}
          >
            <TextArea
              placeholder={t('feedback.animation.suggestionsPlaceholder') || ''}
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
        )}

        {/* 性能数据 */}
        {performanceData && (
          <Form.Item
            name="includePerformanceData"
            valuePropName="checked"
            initialValue={true}
          >
            <Checkbox
              onChange={(e) => setIncludePerformanceData(e.target.checked)}
            >
              {t('feedback.animation.includePerformanceData') || ''}
              <Tooltip title={t('feedback.animation.performanceDataTooltip') || ''}>
                <QuestionCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </Checkbox>
          </Form.Item>
        )}

        {/* 性能数据预览 */}
        {performanceData && includePerformanceData && (
          <Collapse ghost>
            <Panel header={t('feedback.animation.performanceDataPreview') || ''} key="1">
              <pre style={{ maxHeight: '200px', overflow: 'auto', fontSize: '12px' }}>
                {JSON.stringify(performanceData, null, 2)}
              </pre>
            </Panel>
          </Collapse>
        )}

        {/* 截图上传 */}
        <Form.Item
          name="screenshots"
          label={t('feedback.form.screenshots') || ''}
        >
          <Upload
            listType="picture"
            maxCount={3}
            beforeUpload={() => false} // 阻止自动上传
          >
            <Button icon={<UploadOutlined />}>{t('feedback.form.uploadScreenshot') || ''}</Button>
          </Upload>
        </Form.Item>

        {/* 联系许可 */}
        <Form.Item
          name="allowContact"
          valuePropName="checked"
        >
          <Checkbox>{t('feedback.form.allowContact') || ''}</Checkbox>
        </Form.Item>

        <Divider />

        {/* 提交按钮 */}
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SendOutlined />}
            loading={submitting}
            block
          >
            {t('feedback.form.submit') || ''}
          </Button>
        </Form.Item>
      </Form>

      {/* 成功提示 */}
      <Modal
        title={t('feedback.success.title') || ''}
        open={showSuccessModal}
        onCancel={handleCloseSuccessModal}
        footer={[
          <Button key="close" type="primary" onClick={handleCloseSuccessModal} destroyOnClose keyboard={true} maskClosable={true}>
            {t('feedback.success.close') || ''}
          </Button>
        ]}
      >
        <p>{t('feedback.success.message') || ''}</p>
        <p>{t('feedback.animation.success.additionalMessage') || ''}</p>
      </Modal>
    </div>
  );
};

export default AnimationFeedbackForm;
