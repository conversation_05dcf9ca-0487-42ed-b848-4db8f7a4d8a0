{"timestamp": "2025-09-23T20:03:20.942Z", "totalFiles": 1097, "modalFiles": 72, "totalIssues": 487, "problemFiles": [{"file": "components\\AnimationEditor\\BlendSpace1DEditor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleFormSubmit 未使用 useCallback", "事件处理函数 handleAddNode 未使用 useCallback", "事件处理函数 handleEditNode 未使用 useCallback", "事件处理函数 handleDeleteNode 未使用 useCallback", "事件处理函数 handleNodeFormSubmit 未使用 useCallback", "事件处理函数 handlePositionChange 未使用 useCallback", "事件处理函数 handlePlayPause 未使用 useCallback", "事件处理函数 handleCanvasClick 未使用 useCallback", "事件处理函数 handleCanvasMouseMove 未使用 useCallback", "事件处理函数 handleCanvasMouseDown 未使用 useCallback", "事件处理函数 handleCanvasMouseUp 未使用 useCallback", "事件处理函数 handleCanvasMouseLeave 未使用 useCallback", "事件处理函数 toggleNodeWeights 未使用 useCallback", "事件处理函数 toggleHeatmap 未使用 useCallback", "事件处理函数 handleCurveTypeChange 未使用 useCallback", "事件处理函数 handleCustomCurveChange 未使用 useCallback", "事件处理函数 handlePresetCurveChange 未使用 useCallback", "事件处理函数 handleBezierCurveChange 未使用 useCallback"]}, {"file": "components\\AnimationEditor\\BlendSpace2DEditor.tsx", "issues": ["事件处理函数 handleCanvasClick 未使用 useCallback", "事件处理函数 handleCanvasMouseMove 未使用 useCallback", "事件处理函数 handleCanvasMouseDown 未使用 useCallback", "事件处理函数 handleCanvasMouseUp 未使用 useCallback", "事件处理函数 handleCanvasMouseLeave 未使用 useCallback", "事件处理函数 toggleNodeWeights 未使用 useCallback", "事件处理函数 toggleHeatmap 未使用 useCallback", "事件处理函数 toggleTriangulation 未使用 useCallback", "事件处理函数 handlePlayPause 未使用 useCallback", "事件处理函数 handleCurveTypeChange 未使用 useCallback", "事件处理函数 handleCustomCurveChange 未使用 useCallback", "事件处理函数 handlePresetCurveChange 未使用 useCallback", "事件处理函数 handleBezierCurveChange 未使用 useCallback", "事件处理函数 handleFormSubmit 未使用 useCallback", "事件处理函数 handleAddNode 未使用 useCallback", "事件处理函数 handleEditNode 未使用 useCallback", "事件处理函数 handleDeleteNode 未使用 useCallback", "事件处理函数 handleNodeFormSubmit 未使用 useCallback", "事件处理函数 handlePositionChange 未使用 useCallback"]}, {"file": "components\\AnimationEditor\\StateMachineEditor.tsx", "issues": ["事件处理函数 handleSave 未使用 useCallback", "事件处理函数 handleCancel 未使用 useCallback", "事件处理函数 handleAddState 未使用 useCallback", "事件处理函数 handleEditState 未使用 useCallback", "事件处理函数 handleDeleteState 未使用 useCallback", "事件处理函数 handleAddTransition 未使用 useCallback", "事件处理函数 handleEditTransition 未使用 useCallback", "事件处理函数 handleDeleteTransition 未使用 useCallback", "事件处理函数 handleAddParameter 未使用 useCallback", "事件处理函数 handleStateFormSubmit 未使用 useCallback", "事件处理函数 handleTransitionFormSubmit 未使用 useCallback", "事件处理函数 handleParameterFormSubmit 未使用 useCallback", "事件处理函数 handleStateClick 未使用 useCallback", "事件处理函数 handleTransitionClick 未使用 useCallback", "事件处理函数 handleStateDrag 未使用 useCallback", "事件处理函数 handleCanvasMouseDown 未使用 useCallback", "事件处理函数 handleCanvasMouseMove 未使用 useCallback", "事件处理函数 handleCanvasMouseUp 未使用 useCallback", "事件处理函数 handleCanvasMouseLeave 未使用 useCallback", "事件处理函数 handleCanvasWheel 未使用 useCallback", "事件处理函数 handleResetZoom 未使用 useCallback", "事件处理函数 handleToggleFullscreen 未使用 useCallback", "事件处理函数 handleToggleDebug 未使用 useCallback"]}, {"file": "components\\AssetsPanel\\index.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleUpload 未使用 useCallback", "事件处理函数 handleDelete 未使用 useCallback"]}, {"file": "components\\avatar\\ActionEditor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleAddAction 未使用 useCallback", "事件处理函数 handleEditAction 未使用 useCallback", "事件处理函数 handleDeleteAction 未使用 useCallback", "事件处理函数 handlePlayAction 未使用 useCallback", "事件处理函数 handleStopAction 未使用 useCallback", "事件处理函数 handleSaveAction 未使用 useCallback", "事件处理函数 handleAddEvent 未使用 useCallback", "事件处理函数 handleEditEvent 未使用 useCallback", "事件处理函数 handleDeleteEvent 未使用 useCallback", "事件处理函数 handleSaveEvent 未使用 useCallback", "事件处理函数 handleExportActions 未使用 useCallback", "事件处理函数 handleImportActions 未使用 useCallback"]}, {"file": "components\\avatar\\ActionRecordingPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleStartRecording 未使用 useCallback", "事件处理函数 handlePauseRecording 未使用 useCallback", "事件处理函数 handleResumeRecording 未使用 useCallback", "事件处理函数 handleStopRecording 未使用 useCallback", "事件处理函数 handlePlayRecording 未使用 useCallback", "事件处理函数 handlePausePlayback 未使用 useCallback", "事件处理函数 handleResumePlayback 未使用 useCallback", "事件处理函数 handleStopPlayback 未使用 useCallback", "事件处理函数 handleDeleteRecording 未使用 useCallback", "事件处理函数 handleImportRecording 未使用 useCallback", "事件处理函数 handleExportRecording 未使用 useCallback", "事件处理函数 renderRecordingControls 未使用 useCallback", "事件处理函数 renderPlaybackControls 未使用 useCallback"]}, {"file": "components\\avatar\\ControllerPresetSelector.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 toggleFavorite 未使用 useCallback"]}, {"file": "components\\avatar\\EnvironmentAwarenessEditor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleAddRule 未使用 useCallback", "事件处理函数 handleEditRule 未使用 useCallback", "事件处理函数 handleDeleteRule 未使用 useCallback", "事件处理函数 handleSaveRule 未使用 useCallback", "事件处理函数 handleExportRules 未使用 useCallback", "事件处理函数 handleImportRules 未使用 useCallback"]}, {"file": "components\\collaboration\\ConflictResolutionDialog.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 getOperationTypeName 未使用 useCallback", "事件处理函数 getOperationDescription 未使用 useCallback", "事件处理函数 handleStrategyChange 未使用 useCallback", "事件处理函数 handleConfirm 未使用 useCallback", "事件处理函数 handleCancel 未使用 useCallback", "事件处理函数 applyAiSuggestion 未使用 useCallback", "事件处理函数 renderOperationData 未使用 useCallback", "事件处理函数 renderDeletionConflictPreview 未使用 useCallback", "事件处理函数 renderAiSuggestions 未使用 useCallback"]}, {"file": "components\\collaboration\\LockPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleReleaseLock 未使用 useCallback", "事件处理函数 handleForciblyReleaseLock 未使用 useCallback", "事件处理函数 handleSettingsChange 未使用 useCallback"]}, {"file": "components\\collaboration\\OrganizationPermissionPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleNodeAdded 未使用 useCallback", "事件处理函数 handleNodeUpdated 未使用 useCallback", "事件处理函数 handleNodeRemoved 未使用 useCallback", "事件处理函数 handleInheritanceStrategyChange 未使用 useCallback", "事件处理函数 handleEnableOrganizationPermissions 未使用 useCallback", "事件处理函数 getNodeIcon 未使用 useCallback", "事件处理函数 handleNodeSelect 未使用 useCallback", "事件处理函数 handleNodeExpand 未使用 useCallback", "事件处理函数 handleAddNode 未使用 useCallback", "事件处理函数 handleEditNode 未使用 useCallback", "事件处理函数 handleDeleteNode 未使用 useCallback", "事件处理函数 handleNodeFormSubmit 未使用 useCallback"]}, {"file": "components\\collaboration\\PermissionLogPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 getPermissionName 未使用 useCallback", "事件处理函数 handleClearLogs 未使用 useCallback", "事件处理函数 handleToggleLogging 未使用 useCallback"]}, {"file": "components\\collaboration\\SceneVersionComparePanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 renderVersionInfo 未使用 useCallback", "事件处理函数 handleShowRollbackModal 未使用 useCallback", "事件处理函数 handleRollback 未使用 useCallback", "事件处理函数 handleMergeVersions 未使用 useCallback", "事件处理函数 handleShowMergeModal 未使用 useCallback", "事件处理函数 exportComparisonResult 未使用 useCallback", "事件处理函数 openComponentComparePanel 未使用 useCallback", "事件处理函数 handleComponentCompareExport 未使用 useCallback", "事件处理函数 compareComponents 未使用 useCallback"]}, {"file": "components\\collaboration\\UserInteractionAnalysisPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 loadUserActions 未使用 useCallback", "事件处理函数 handleLogAdded 未使用 useCallback", "事件处理函数 handleAnalyticsUpdated 未使用 useCallback", "事件处理函数 handleViewLogDetail 未使用 useCallback", "事件处理函数 handleClearLogs 未使用 useCallback", "事件处理函数 handleToggleLogging 未使用 useCallback", "事件处理函数 handleExportLogs 未使用 useCallback"]}, {"file": "components\\common\\TextureSelector.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleTextureChange 未使用 useCallback", "事件处理函数 handleInputChange 未使用 useCallback", "事件处理函数 handleClear 未使用 useCallback", "事件处理函数 handlePreview 未使用 useCallback", "事件处理函数 textureSelectorContent 未使用 useCallback"]}, {"file": "components\\debug\\HistoryPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSelectionChange 未使用 useCallback", "事件处理函数 getOptimizationTypeTag 未使用 useCallback", "事件处理函数 renderComparisonResult 未使用 useCallback"]}, {"file": "components\\debug\\PerformanceComparisonPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSnapshotCreated 未使用 useCallback", "事件处理函数 handleSnapshotDeleted 未使用 useCallback", "事件处理函数 handleComparisonCompleted 未使用 useCallback", "事件处理函数 handleSnapshotCreateSubmit 未使用 useCallback", "事件处理函数 confirmDeleteSnapshot 未使用 useCallback", "事件处理函数 handleDeleteConfirm 未使用 useCallback"]}, {"file": "components\\debug\\PerformanceTestAutomationPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 seconds 未使用 useCallback", "事件处理函数 handleTestStarted 未使用 useCallback", "事件处理函数 handleTestCompleted 未使用 useCallback", "事件处理函数 handleTestFailed 未使用 useCallback", "事件处理函数 handleTestProgress 未使用 useCallback", "事件处理函数 handleTestEditSubmit 未使用 useCallback", "事件处理函数 confirmDeleteTest 未使用 useCallback", "事件处理函数 handleDeleteConfirm 未使用 useCallback", "事件处理函数 handleExportConfirm 未使用 useCallback"]}, {"file": "components\\debug\\ServiceStatusMonitor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleHealthCheck 未使用 useCallback", "事件处理函数 handleServiceStatusChange 未使用 useCallback", "事件处理函数 getStatusIcon 未使用 useCallback"]}, {"file": "components\\environment\\EnvironmentPresetPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSearch 未使用 useCallback", "事件处理函数 handleCategoryChange 未使用 useCallback", "事件处理函数 handleApplyPreset 未使用 useCallback", "事件处理函数 handleCreateCustomPreset 未使用 useCallback", "事件处理函数 handleEditCustomPreset 未使用 useCallback", "事件处理函数 handleDuplicatePreset 未使用 useCallback", "事件处理函数 handleDeleteCustomPreset 未使用 useCallback", "事件处理函数 handleSaveCustomPreset 未使用 useCallback", "事件处理函数 getResponseTypeColor 未使用 useCallback"]}, {"file": "components\\ExampleBrowser\\ExampleDetail.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleFavoriteClick 未使用 useCallback", "事件处理函数 handlePreviewClick 未使用 useCallback", "事件处理函数 handleShareClick 未使用 useCallback", "事件处理函数 handleDownloadClick 未使用 useCallback"]}, {"file": "components\\ExampleBrowser\\ImportDialog.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleWorkspaceChange 未使用 useCallback", "事件处理函数 handleImportOptionChange 未使用 useCallback", "事件处理函数 handleIncludeAssetsChange 未使用 useCallback", "事件处理函数 handleCancel 未使用 useCallback", "事件处理函数 handleBrowseFolder 未使用 useCallback"]}, {"file": "components\\FacialAnimationEditor\\FacialAnimationEditorPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 updateAnimation 未使用 useCallback", "事件处理函数 handlePlayPause 未使用 useCallback", "事件处理函数 handleJumpToStart 未使用 useCallback", "事件处理函数 handleJumpToEnd 未使用 useCallback", "事件处理函数 handleSaveAnimation 未使用 useCallback", "事件处理函数 handleImportAnimation 未使用 useCallback", "事件处理函数 handleExportAnimation 未使用 useCallback", "事件处理函数 handleCreateNewAnimation 未使用 useCallback", "事件处理函数 handleOpenAIGenerator 未使用 useCallback", "事件处理函数 handleCloseAIGenerator 未使用 useCallback"]}, {"file": "components\\FacialAnimationEditor\\FacialAnimationTemplateManager.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 renderParameterControl 未使用 useCallback"]}, {"file": "components\\feedback\\AnimationFeedbackForm.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCloseSuccessModal 未使用 useCallback", "事件处理函数 handleFeedbackTypeChange 未使用 useCallback"]}, {"file": "components\\feedback\\ContextAwareFeedbackButton.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 getIcon 未使用 useCallback", "事件处理函数 captureContext 未使用 useCallback", "事件处理函数 handleSuccess 未使用 useCallback", "事件处理函数 getContextTooltip 未使用 useCallback", "事件处理函数 renderButton 未使用 useCallback", "事件处理函数 button 未使用 useCallback"]}, {"file": "components\\feedback\\ContextAwareFeedbackForm.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 captureContext 未使用 useCallback", "事件处理函数 handleFeedbackTypeChange 未使用 useCallback", "事件处理函数 handleCloseSuccessModal 未使用 useCallback", "事件处理函数 renderContextPreview 未使用 useCallback"]}, {"file": "components\\feedback\\FeedbackForm.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCloseSuccessModal 未使用 useCallback"]}, {"file": "components\\git\\GitBranchPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCreateBranch 未使用 useCallback", "事件处理函数 handleConfirmCreateBranch 未使用 useCallback", "事件处理函数 handleCancelCreateBranch 未使用 useCallback", "事件处理函数 handleCheckoutBranch 未使用 useCallback", "事件处理函数 handleDeleteBranch 未使用 useCallback", "事件处理函数 handleMergeBranch 未使用 useCallback", "事件处理函数 handleConfirmMergeBranch 未使用 useCallback", "事件处理函数 handleCancelMergeBranch 未使用 useCallback", "事件处理函数 handlePushBranch 未使用 useCallback", "事件处理函数 handlePullBranch 未使用 useCallback"]}, {"file": "components\\git\\GitConflictResolver.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleClose 未使用 useCallback", "事件处理函数 handleConfirmClose 未使用 useCallback", "事件处理函数 handleCancelClose 未使用 useCallback", "事件处理函数 handlePrevConflict 未使用 useCallback", "事件处理函数 handleNextConflict 未使用 useCallback", "事件处理函数 handleResolveConflict 未使用 useCallback", "事件处理函数 handleResolveAllConflicts 未使用 useCallback", "事件处理函数 handleEditCustomContent 未使用 useCallback", "事件处理函数 handleSaveCustomContent 未使用 useCallback", "事件处理函数 renderConflictContent 未使用 useCallback"]}, {"file": "components\\git\\GitHistoryPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSearch 未使用 useCallback", "事件处理函数 handleSort 未使用 useCallback", "事件处理函数 handleViewCommitDetail 未使用 useCallback", "事件处理函数 handleCloseDetailModal 未使用 useCallback", "事件处理函数 handleCopyHash 未使用 useCallback", "事件处理函数 handleViewDiff 未使用 useCallback", "事件处理函数 handleRollback 未使用 useCallback"]}, {"file": "components\\git\\GitStatusPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleStageFiles 未使用 useCallback", "事件处理函数 handleUnstageFiles 未使用 useCallback", "事件处理函数 handleCommit 未使用 useCallback", "事件处理函数 handleConfirmCommit 未使用 useCallback", "事件处理函数 handleCancelCommit 未使用 useCallback", "事件处理函数 handlePull 未使用 useCallback", "事件处理函数 handlePush 未使用 useCallback", "事件处理函数 handleSync 未使用 useCallback", "事件处理函数 handleViewDiff 未使用 useCallback", "事件处理函数 renderFileIcon 未使用 useCallback"]}, {"file": "components\\interaction\\InteractionManager.tsx", "issues": ["事件处理函数 handleInteraction 未使用 useCallback", "事件处理函数 handleStatsUpdate 未使用 useCallback"]}, {"file": "components\\layout\\EditorLayout.tsx", "issues": ["事件处理函数 handleToggleFullscreen 未使用 useCallback", "事件处理函数 handleTogglePlay 未使用 useCallback", "事件处理函数 handleSetTransformMode 未使用 useCallback", "事件处理函数 handleSetTransformSpace 未使用 useCallback", "事件处理函数 handleSetSnapMode 未使用 useCallback", "事件处理函数 handleToggleGrid 未使用 useCallback", "事件处理函数 handleToggleAxes 未使用 useCallback", "事件处理函数 handleUndo 未使用 useCallback", "事件处理函数 handleRedo 未使用 useCallback", "事件处理函数 handleLayoutChange 未使用 useCallback", "事件处理函数 handleResetLayout 未使用 useCallback", "事件处理函数 handleToggleTheme 未使用 useCallback", "事件处理函数 handleLoadLayout 未使用 useCallback"]}, {"file": "components\\layout\\MobileAdaptiveLayout.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleBreakpointChange 未使用 useCallback", "事件处理函数 handleControlSizeChange 未使用 useCallback", "事件处理函数 handleTouchModeChange 未使用 useCallback", "事件处理函数 handleGyroscopeEnabledChange 未使用 useCallback", "事件处理函数 toggleControlPanel 未使用 useCallback"]}, {"file": "components\\MaterialEditor\\MaterialEditor.example.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSave 未使用 useCallback"]}, {"file": "components\\network\\NetworkLatencySimulator.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleFormChange 未使用 useCallback", "事件处理函数 handleApplyConfig 未使用 useCallback", "事件处理函数 handleResetConfig 未使用 useCallback", "事件处理函数 handleOpenScenarioModal 未使用 useCallback", "事件处理函数 handleStartScenario 未使用 useCallback", "事件处理函数 handleStopScenario 未使用 useCallback"]}, {"file": "components\\network\\NetworkSimulatorPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleFormChange 未使用 useCallback", "事件处理函数 handleApply 未使用 useCallback", "事件处理函数 handleReset 未使用 useCallback", "事件处理函数 handlePresetChange 未使用 useCallback", "事件处理函数 handleOpenScenarioModal 未使用 useCallback", "事件处理函数 handleCloseScenarioModal 未使用 useCallback", "事件处理函数 handleSelectScenario 未使用 useCallback", "事件处理函数 handleStartScenario 未使用 useCallback", "事件处理函数 handleStopScenario 未使用 useCallback", "事件处理函数 handleOpenSaveScenarioModal 未使用 useCallback", "事件处理函数 handleCloseSaveScenarioModal 未使用 useCallback", "事件处理函数 handleSaveScenario 未使用 useCallback"]}, {"file": "components\\panels\\AssetsPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSearch 未使用 useCallback", "事件处理函数 handleFolderClick 未使用 useCallback", "事件处理函数 handleAssetSelect 未使用 useCallback", "事件处理函数 handleAssetDoubleClick 未使用 useCallback", "事件处理函数 getAssetIcon 未使用 useCallback"]}, {"file": "components\\panels\\InstancesPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCreateTemplate 未使用 useCallback", "事件处理函数 handleCreateInstance 未使用 useCallback", "事件处理函数 handleEditInstance 未使用 useCallback", "事件处理函数 handleUpdateParameters 未使用 useCallback", "事件处理函数 handleDeleteTemplate 未使用 useCallback", "事件处理函数 handleDeleteInstance 未使用 useCallback", "事件处理函数 handleToggleInstanceVisibility 未使用 useCallback", "事件处理函数 handleOpenEditInstanceModal 未使用 useCallback", "事件处理函数 handleOpenParametersModal 未使用 useCallback"]}, {"file": "components\\panels\\LayersPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCreateLayer 未使用 useCallback", "事件处理函数 handleCreateLayerGroup 未使用 useCallback", "事件处理函数 handleEditLayer 未使用 useCallback", "事件处理函数 handleDeleteLayer 未使用 useCallback", "事件处理函数 handleToggleVisibility 未使用 useCallback", "事件处理函数 handleToggleLock 未使用 useCallback", "事件处理函数 handleMoveLayer 未使用 useCallback", "事件处理函数 handleOpenEditModal 未使用 useCallback", "事件处理函数 handleToggleExpanded 未使用 useCallback"]}, {"file": "components\\ParticleEditor\\example.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCreateNew 未使用 useCallback", "事件处理函数 handleEdit 未使用 useCallback", "事件处理函数 handleSave 未使用 useCallback", "事件处理函数 handleCancel 未使用 useCallback", "事件处理函数 handleDelete 未使用 useCallback"]}, {"file": "components\\physics\\PhysicsMaterialEditor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleValuesChange 未使用 useCallback", "事件处理函数 handleSaveMaterial 未使用 useCallback", "事件处理函数 handleCreateMaterial 未使用 useCallback", "事件处理函数 handleDeleteMaterial 未使用 useCallback", "事件处理函数 handleDuplicateMaterial 未使用 useCallback", "事件处理函数 handleExportMaterial 未使用 useCallback", "事件处理函数 handleImportMaterial 未使用 useCallback", "事件处理函数 handleMaterialSelectChange 未使用 useCallback"]}, {"file": "components\\physics\\PhysicsPresetEditor.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleEditPreset 未使用 useCallback", "事件处理函数 handleDuplicatePreset 未使用 useCallback", "事件处理函数 handleDeletePreset 未使用 useCallback", "事件处理函数 handleSavePreset 未使用 useCallback", "事件处理函数 handleCreatePreset 未使用 useCallback", "事件处理函数 handleExportAllPresets 未使用 useCallback", "事件处理函数 handleImportPresets 未使用 useCallback"]}, {"file": "components\\rag\\KnowledgeBasePanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 getFileTypeIcon 未使用 useCallback", "事件处理函数 handleKnowledgeSelect 未使用 useCallback", "事件处理函数 handleKnowledgeEdit 未使用 useCallback"]}, {"file": "components\\rendering\\water\\WaterMaterialPresetSelector.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 toggleFavorite 未使用 useCallback", "事件处理函数 handleApplyPreset 未使用 useCallback"]}, {"file": "components\\resources\\CharacterResourceBrowser.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleTagSelect 未使用 useCallback", "事件处理函数 showAnimationDetail 未使用 useCallback", "事件处理函数 renderAnimationCard 未使用 useCallback", "事件处理函数 renderAnimationDetail 未使用 useCallback"]}, {"file": "components\\scene\\SceneGenerationPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 loadGenerationHistory 未使用 useCallback", "事件处理函数 saveGenerationHistory 未使用 useCallback", "事件处理函数 handleSceneGenerated 未使用 useCallback", "事件处理函数 handleVoiceSceneGenerated 未使用 useCallback", "事件处理函数 handleTextSceneGenerated 未使用 useCallback", "事件处理函数 toggleFavorite 未使用 useCallback", "事件处理函数 triggerTextRegeneration 未使用 useCallback", "事件处理函数 triggerVoiceRegeneration 未使用 useCallback", "事件处理函数 renderQuickActions 未使用 useCallback"]}, {"file": "components\\scripting\\ScriptEditor.tsx", "issues": ["事件处理函数 getDefaultScriptContent 未使用 useCallback", "事件处理函数 renderExecutionStatus 未使用 useCallback"]}, {"file": "components\\scripting\\ScriptTemplates.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 toggleFavorite 未使用 useCallback", "事件处理函数 handleSelectTemplate 未使用 useCallback", "事件处理函数 handlePreviewTemplate 未使用 useCallback"]}, {"file": "components\\settings\\ScriptEditorSettingsPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleShortcutToggle 未使用 useCallback", "事件处理函数 handleClearCache 未使用 useCallback", "事件处理函数 handleExportSettings 未使用 useCallback"]}, {"file": "components\\terrain\\TerrainComponentManager.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleDeleteTerrain 未使用 useCallback", "事件处理函数 handleTerrainModified 未使用 useCallback", "事件处理函数 handleOperation 未使用 useCallback"]}, {"file": "components\\terrain\\TerrainImportExportPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleTabChange 未使用 useCallback", "事件处理函数 handleHeightMapFormatChange 未使用 useCallback", "事件处理函数 handleThirdPartyFormatChange 未使用 useCallback", "事件处理函数 handleExportOptionChange 未使用 useCallback", "事件处理函数 handlePreview 未使用 useCallback", "事件处理函数 getFormatOptions 未使用 useCallback", "事件处理函数 getExportOptions 未使用 useCallback", "事件处理函数 handleExportJSON 未使用 useCallback", "事件处理函数 getFileExtension 未使用 useCallback"]}, {"file": "components\\testing\\FeedbackForm.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleFeedbackTypeChange 未使用 useCallback", "事件处理函数 handleCancel 未使用 useCallback", "事件处理函数 renderFormContent 未使用 useCallback", "事件处理函数 getFeedbackContentLabel 未使用 useCallback", "事件处理函数 getFeedbackContentPlaceholder 未使用 useCallback"]}, {"file": "components\\testing\\UserTestingPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSessionStarted 未使用 useCallback", "事件处理函数 handleTaskCompleted 未使用 useCallback", "事件处理函数 handleAllTasksCompleted 未使用 useCallback", "事件处理函数 handleToggleTesting 未使用 useCallback", "事件处理函数 handleToggleRecording 未使用 useCallback", "事件处理函数 handleTaskClick 未使用 useCallback", "事件处理函数 handleCompleteTask 未使用 useCallback", "事件处理函数 handleSubmitFeedback 未使用 useCallback", "事件处理函数 handleCloseFeedbackForm 未使用 useCallback", "事件处理函数 handleGenerateReport 未使用 useCallback", "事件处理函数 handleViewHistorySession 未使用 useCallback"]}, {"file": "components\\tutorials\\ProjectTutorialPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleProjectViewed 未使用 useCallback", "事件处理函数 handleProjectClick 未使用 useCallback", "事件处理函数 handleImportProject 未使用 useCallback", "事件处理函数 handleFavoriteProject 未使用 useCallback"]}, {"file": "components\\tutorials\\TutorialPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleTutorialStarted 未使用 useCallback", "事件处理函数 handleTutorialCompleted 未使用 useCallback", "事件处理函数 handleTutorialExited 未使用 useCallback", "事件处理函数 handleStepChanged 未使用 useCallback", "事件处理函数 handleTutorialItemSelect 未使用 useCallback", "事件处理函数 handleSeriesSelect 未使用 useCallback", "事件处理函数 renderPanelContent 未使用 useCallback"]}, {"file": "components\\tutorials\\TutorialSeriesPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleItemCompleted 未使用 useCallback", "事件处理函数 handleTutorialItemClick 未使用 useCallback", "事件处理函数 getTutorialTypeIcon 未使用 useCallback"]}, {"file": "components\\tutorials\\VideoTutorialList.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleTutorialWatched 未使用 useCallback", "事件处理函数 handleTutorialSelect 未使用 useCallback", "事件处理函数 handlePlayerClose 未使用 useCallback", "事件处理函数 handleSearch 未使用 useCallback"]}, {"file": "components\\ui\\UIPresetSelector.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSelectPreset 未使用 useCallback", "事件处理函数 handlePreviewPreset 未使用 useCallback", "事件处理函数 getCategoryOptions 未使用 useCallback", "事件处理函数 getTypeOptions 未使用 useCallback"]}, {"file": "components\\visualscript\\DebugPanel.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleAddBreakpoint 未使用 useCallback", "事件处理函数 handleEditBreakpoint 未使用 useCallback", "事件处理函数 handleSaveBreakpoint 未使用 useCallback", "事件处理函数 handleAddWatch 未使用 useCallback", "事件处理函数 renderExecutionPath 未使用 useCallback"]}, {"file": "pages\\AnimationLibraryPage.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSaveAnimation 未使用 useCallback", "事件处理函数 handleDeleteAnimation 未使用 useCallback", "事件处理函数 handleDuplicateAnimation 未使用 useCallback", "事件处理函数 handlePreviewAnimation 未使用 useCallback"]}, {"file": "pages\\Editor\\index.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleMenuClick 未使用 useCallback", "事件处理函数 handlePanelChange 未使用 useCallback"]}, {"file": "pages\\MaterialLibraryPage.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSaveMaterial 未使用 useCallback", "事件处理函数 handleDeleteMaterial 未使用 useCallback", "事件处理函数 handleDuplicateMaterial 未使用 useCallback"]}, {"file": "pages\\ParticleLibraryPage.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleSaveParticleSystem 未使用 useCallback", "事件处理函数 handleDeleteParticleSystem 未使用 useCallback", "事件处理函数 handleDuplicateParticleSystem 未使用 useCallback", "事件处理函数 handlePreviewParticleSystem 未使用 useCallback"]}, {"file": "pages\\ProjectsPage.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 handleCreateProject 未使用 useCallback", "事件处理函数 handleCreateScene 未使用 useCallback", "事件处理函数 handleEditProject 未使用 useCallback", "事件处理函数 handleDeleteProject 未使用 useCallback", "事件处理函数 handleOpenProject 未使用 useCallback"]}, {"file": "panels\\FacialAnimationPresetPanel.tsx", "issues": ["事件处理函数 renderQuickActions 未使用 useCallback"]}, {"file": "tools\\TutorialRecordingTool.tsx", "issues": ["有事件处理函数但未导入 useCallback", "事件处理函数 renderRecordingControls 未使用 useCallback"]}]}