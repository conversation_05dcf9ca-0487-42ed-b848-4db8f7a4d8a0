/**
 * 错误边界组件
 * 用于捕获子组件中的JavaScript错误，并显示备用UI
 */
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null};
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新状态，下次渲染时显示备用UI
    return {
      hasError: true,
      error};
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录详细的错误信息
    console.error('错误边界捕获到错误:', error, errorInfo);

    // 如果是Modal相关的错误，尝试清理Modal状态
    if (error.message.includes('Modal') || error.message.includes('关闭') || error.message.includes('onCancel')) {
      console.warn('检测到Modal相关错误，尝试清理Modal状态');
      // 清理可能残留的Modal状态
      try {
        const modals = document.querySelectorAll('.ant-modal-mask');
        modals.forEach(modal => {
          if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
          }
        });
      } catch (cleanupError) {
        console.error('清理Modal状态失败:', cleanupError);
      }
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // 渲染备用UI
      return this.props.fallback;
    }

    return this.props.children;
  }
}
